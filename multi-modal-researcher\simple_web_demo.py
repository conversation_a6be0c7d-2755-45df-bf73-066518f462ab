#!/usr/bin/env python3
"""
Simple web demo for Multi-Modal Researcher (standalone version)
"""

import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class DemoHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_html_page()
        elif parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/config':
            self.send_config()
        elif parsed_path.path == '/research':
            query_params = parse_qs(parsed_path.query)
            topic = query_params.get('topic', ['AI in Healthcare'])[0]
            video_url = query_params.get('video_url', [''])[0]
            self.send_research_result(topic, video_url)
        else:
            self.send_404()
    
    def send_html_page(self):
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Multi-Modal Researcher Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .feature { background: #e8f4fd; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007cba; }
        .demo-form { background: #f9f9f9; padding: 25px; border-radius: 8px; margin: 25px 0; }
        input[type="text"] { width: 100%; padding: 12px; margin: 8px 0; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 12px 25px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .endpoint { background: #e9ecef; padding: 12px; margin: 8px 0; border-radius: 4px; font-family: monospace; }
        .result-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #dee2e6; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .nav-links { text-align: center; margin: 20px 0; }
        .nav-links a { margin: 0 15px; color: #007cba; text-decoration: none; font-weight: bold; }
        .nav-links a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Modal Researcher Demo</h1>
        
        <div class="nav-links">
            <a href="/">🏠 Home</a>
            <a href="/health">💚 Health Check</a>
            <a href="/config">⚙️ Configuration</a>
            <a href="/research?topic=Artificial Intelligence in Healthcare">🔬 Demo Research</a>
        </div>
        
        <div class="feature">
            <h3>🤖 OpenRouter API Integration</h3>
            <p>Access to multiple LLM models (GPT-4, Claude, Gemini, etc.) through a unified API for research and content generation. Supports flexible model selection for different tasks.</p>
        </div>
        
        <div class="feature">
            <h3>🎙️ Douban TTS Integration</h3>
            <p>High-quality Chinese text-to-speech with WebSocket streaming for podcast generation. Supports multiple voice types and audio quality settings.</p>
        </div>
        
        <div class="feature">
            <h3>📊 Multi-Modal Research</h3>
            <p>Combines text research with video context analysis for comprehensive reports. Generates structured markdown reports with citations and executive summaries.</p>
        </div>
        
        <div class="demo-form">
            <h3>🔬 Try Research Demo</h3>
            <form action="/research" method="get">
                <label><strong>Research Topic:</strong></label>
                <input type="text" name="topic" value="Artificial Intelligence in Healthcare" placeholder="Enter your research topic">
                
                <label><strong>Video URL (optional):</strong></label>
                <input type="text" name="video_url" placeholder="https://youtu.be/example" value="">
                
                <button type="submit">🔍 Start Research</button>
            </form>
        </div>
        
        <div class="status warning">
            <strong>⚠️ Demo Mode:</strong> This is running in demonstration mode. To enable full functionality with real AI-generated content, please configure your API keys in the .env file.
        </div>
        
        <div class="result-section">
            <h3>📡 Available API Endpoints</h3>
            <div class="endpoint">GET / - This demo interface</div>
            <div class="endpoint">GET /health - System health and configuration status</div>
            <div class="endpoint">GET /config - Current system configuration</div>
            <div class="endpoint">GET /research?topic=YourTopic&video_url=OptionalURL - Research demo</div>
        </div>
        
        <div class="result-section">
            <h3>🔧 Configuration Setup</h3>
            <p>To enable full functionality with real AI-generated content, configure your API keys in the <code>.env</code> file:</p>
            <pre># OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_SITE_URL=https://your-site.com  # Optional
OPENROUTER_SITE_NAME=Your App Name  # Optional

# Douban TTS API Configuration  
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here</pre>
            
            <p><strong>Getting API Keys:</strong></p>
            <ul>
                <li><strong>OpenRouter:</strong> Sign up at <a href="https://openrouter.ai" target="_blank">openrouter.ai</a> and get your API key</li>
                <li><strong>Douban TTS:</strong> Apply for access at <a href="https://www.volcengine.com/docs/6561/1257584" target="_blank">Volcano Engine</a></li>
            </ul>
        </div>
        
        <div class="result-section">
            <h3>🎯 Key Features</h3>
            <ul>
                <li><strong>Flexible Model Selection:</strong> Choose from GPT-4, Claude, Gemini, and other models</li>
                <li><strong>Research Generation:</strong> Comprehensive topic analysis and report creation</li>
                <li><strong>Video Analysis:</strong> Context-aware video content analysis</li>
                <li><strong>Podcast Creation:</strong> Natural dialogue generation with TTS synthesis</li>
                <li><strong>Multi-language Support:</strong> Optimized for Chinese TTS with Douban API</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_health_check(self):
        # Check environment variables
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")
        
        openrouter_configured = bool(openrouter_key and not openrouter_key.startswith("demo_"))
        douban_configured = bool(douban_appid and douban_token and 
                               not douban_appid.startswith("demo_") and 
                               not douban_token.startswith("demo_"))
        
        health_data = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "apis": {
                "openrouter": {
                    "configured": openrouter_configured,
                    "status": "ready" if openrouter_configured else "needs_configuration"
                },
                "douban_tts": {
                    "configured": douban_configured,
                    "status": "ready" if douban_configured else "needs_configuration"
                }
            },
            "models": {
                "search": "openai/gpt-4o",
                "synthesis": "openai/gpt-4o", 
                "video": "openai/gpt-4o",
                "podcast_script": "openai/gpt-4o"
            },
            "features": {
                "research_generation": True,
                "video_analysis": True,
                "podcast_creation": True,
                "tts_synthesis": douban_configured
            }
        }
        
        self.send_json_response(health_data)
    
    def send_config(self):
        config_data = {
            "system": {
                "name": "Multi-Modal Researcher",
                "version": "1.0.0",
                "mode": "demo" if not os.getenv("OPENROUTER_API_KEY", "").replace("demo_", "") else "production"
            },
            "openrouter": {
                "base_url": "https://openrouter.ai/api/v1",
                "configured": bool(os.getenv("OPENROUTER_API_KEY", "").replace("demo_", "")),
                "models": {
                    "search_model": "openai/gpt-4o",
                    "synthesis_model": "openai/gpt-4o",
                    "video_model": "openai/gpt-4o",
                    "podcast_script_model": "openai/gpt-4o"
                }
            },
            "douban_tts": {
                "cluster": "volcano_tts",
                "host": "openspeech.bytedance.com",
                "configured": bool(os.getenv("DOUBAN_APPID", "").replace("demo_", "") and 
                                 os.getenv("DOUBAN_TOKEN", "").replace("demo_", "")),
                "settings": {
                    "encoding": "wav",
                    "speed_ratio": 1.0,
                    "volume_ratio": 1.0,
                    "rate": 24000,
                    "voices": {
                        "mike": "zh_male_M392_conversation_wvae_bigtts",
                        "sarah": "zh_female_F001_conversation_wvae_bigtts"
                    }
                }
            }
        }
        
        self.send_json_response(config_data)
    
    def send_research_result(self, topic, video_url):
        # Check if APIs are configured
        openrouter_configured = bool(os.getenv("OPENROUTER_API_KEY", "").replace("demo_", ""))
        douban_configured = bool(os.getenv("DOUBAN_APPID", "").replace("demo_", "") and 
                               os.getenv("DOUBAN_TOKEN", "").replace("demo_", ""))
        
        # Generate demo results
        if openrouter_configured:
            report = f"""# Research Report: {topic}

## Executive Summary

[Real OpenRouter API Integration Active]

This research report would contain comprehensive analysis generated by advanced language models through the OpenRouter API. The system would analyze the topic "{topic}" using the configured model and provide detailed insights, key findings, and expert analysis.

## Key Findings

1. **Primary Insights**: Real AI-generated analysis would appear here
2. **Current Trends**: Latest developments and trends in the field
3. **Expert Perspectives**: Synthesized viewpoints from multiple sources
4. **Future Implications**: Projected developments and considerations

## Video Analysis
{f"- **URL**: {video_url}" if video_url else "- No video provided for analysis"}
{f"- **Content Analysis**: Real video analysis would be performed using the configured model" if video_url else ""}

## Sources and Citations
- Real sources would be gathered and cited here
- Multiple authoritative references would be included
- Citations would be properly formatted and verified

---
*Report generated using OpenRouter API with {os.getenv("OPENROUTER_API_KEY", "")[:10]}...*
"""
            
            podcast_script = f"""Real Podcast Script for: {topic}

Mike: Welcome to our research discussion. Today we're exploring {topic}. Dr. Sarah, this is fascinating research.

Dr. Sarah: Thank you, Mike. [Real AI-generated content would provide expert analysis here using the OpenRouter API]

Mike: What are the most significant developments in this area?

Dr. Sarah: [Comprehensive AI-generated response based on real research would appear here]

Mike: How do you see this impacting the future?

Dr. Sarah: [Expert AI-generated insights about future implications would be provided]

Mike: Thank you for those insights, Dr. Sarah.

Dr. Sarah: My pleasure, Mike. This research shows tremendous potential.

[Generated using OpenRouter API - Real conversation would be much more detailed and informative]
"""
        else:
            report = f"""# Research Report: {topic}

## Demo Mode - Configuration Required

This is a demonstration of the Multi-Modal Researcher system. To generate real AI-powered research reports, please configure your OpenRouter API key in the .env file.

## What You Would Get With Full Configuration:

1. **Comprehensive Analysis**: Deep dive into {topic} using advanced language models
2. **Multi-Source Research**: Information gathered from multiple authoritative sources  
3. **Expert Synthesis**: AI-generated expert-level analysis and insights
4. **Current Trends**: Latest developments and emerging patterns
5. **Future Projections**: Informed predictions about future developments

## Video Analysis
{f"- **URL**: {video_url}" if video_url else "- No video provided for analysis"}
{f"- **Analysis**: Video content would be analyzed for relevant insights about {topic}" if video_url else ""}

## Demo Features Active:
✅ System architecture and workflow
✅ API integration framework  
✅ Configuration management
✅ Report generation structure
✅ Podcast script creation

## To Enable Full Functionality:
1. Get an OpenRouter API key from https://openrouter.ai
2. Get Douban TTS credentials from Volcano Engine
3. Configure your .env file with the API keys
4. Restart the system

---
*Demo mode - Configure API keys for full AI-powered functionality*
"""
            
            podcast_script = f"""Demo Podcast Script for: {topic}

Mike: Welcome to our research discussion. Today we're exploring {topic}. Dr. Sarah, could you give us an overview?

Dr. Sarah: Thank you, Mike. This is currently running in demo mode. To get real AI-generated expert analysis, please configure your OpenRouter API key.

Mike: What would listeners learn about this topic with full functionality?

Dr. Sarah: With the OpenRouter API configured, I would provide comprehensive insights about {topic}, including current research, expert perspectives, and future implications. The system supports multiple advanced models like GPT-4, Claude, and others.

Mike: And what about the audio generation?

Dr. Sarah: The Douban TTS integration would provide high-quality Chinese voice synthesis, creating natural-sounding conversations like this one, but with real expert content.

Mike: How can users get started?

Dr. Sarah: Simply configure the API keys in the .env file, and the system will provide full AI-powered research and podcast generation capabilities.

Mike: Thank you for that overview, Dr. Sarah.

Dr. Sarah: My pleasure, Mike. Configure those API keys to unlock the full potential!

---
[Demo Mode - Configure API keys for real AI-generated content]
"""
        
        result = {
            "topic": topic,
            "video_url": video_url or "Not provided",
            "report": report,
            "podcast_script": podcast_script,
            "status": "success" if openrouter_configured else "demo",
            "configuration": {
                "openrouter_configured": openrouter_configured,
                "douban_configured": douban_configured,
                "mode": "production" if openrouter_configured else "demo"
            },
            "next_steps": [
                "Configure OpenRouter API key for real AI research",
                "Configure Douban TTS credentials for voice synthesis", 
                "Test with your own research topics",
                "Explore different model options through OpenRouter"
            ] if not openrouter_configured else [
                "System fully configured and ready",
                "Try different research topics",
                "Experiment with video URL analysis",
                "Generate podcasts with TTS"
            ]
        }
        
        self.send_json_response(result)
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
    
    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>404 Not Found</h1><p>The requested resource was not found.</p>')
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"[{self.address_string()}] {format % args}")

def start_server():
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, DemoHandler)
    print("🚀 Multi-Modal Researcher Web Demo")
    print("=" * 60)
    print(f"🌐 Server running at: http://127.0.0.1:8000")
    print(f"📚 API endpoints available:")
    print(f"  - Health Check: http://127.0.0.1:8000/health")
    print(f"  - Configuration: http://127.0.0.1:8000/config") 
    print(f"  - Research Demo: http://127.0.0.1:8000/research?topic=YourTopic")
    print("=" * 60)
    print("🔧 Configure API keys in .env file for full functionality")
    print("⚡ Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == "__main__":
    start_server()
