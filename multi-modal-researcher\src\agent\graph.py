"""LangGraph implementation of the research and podcast generation workflow"""

from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import RunnableConfig

from agent.state import ResearchState, ResearchStateInput, ResearchStateOutput
from agent.utils import display_openrouter_response, create_podcast_discussion, create_research_report, get_openrouter_client
from agent.configuration import Configuration
from langsmith import traceable

@traceable(run_type="llm", name="Web Research", project_name="multi-modal-researcher")
def search_research_node(state: ResearchState, config: RunnableConfig) -> dict:
    """Node that performs web search research on the topic"""
    configuration = Configuration.from_runnable_config(config)
    topic = state["topic"]

    # Get OpenRouter client
    client = get_openrouter_client(configuration)

    # Prepare headers for OpenRouter
    extra_headers = {}
    if configuration.openrouter_site_url:
        extra_headers["HTTP-Referer"] = configuration.openrouter_site_url
    if configuration.openrouter_site_name:
        extra_headers["X-Title"] = configuration.openrouter_site_name

    # Note: Open<PERSON>outer doesn't have built-in Google Search like Gemini
    # We'll need to implement web search separately or use a different approach
    search_prompt = f"""
    Research this topic and provide a comprehensive overview: {topic}

    Please provide:
    1. Key concepts and definitions
    2. Current trends and developments
    3. Important applications or use cases
    4. Notable experts or organizations in this field
    5. Recent developments or news

    Make your response informative and well-structured.
    """

    search_response = client.chat.completions.create(
        extra_headers=extra_headers,
        model=configuration.search_model,
        messages=[{"role": "user", "content": search_prompt}],
        temperature=configuration.search_temperature
    )

    search_text, search_sources_text = display_openrouter_response(search_response, search_enabled=True)

    return {
        "search_text": search_text,
        "search_sources_text": search_sources_text
    }


@traceable(run_type="llm", name="YouTube Video Analysis", project_name="multi-modal-researcher")
def analyze_video_node(state: ResearchState, config: RunnableConfig) -> dict:
    """Node that analyzes video content if video URL is provided"""
    configuration = Configuration.from_runnable_config(config)
    video_url = state.get("video_url")
    topic = state["topic"]

    if not video_url:
        return {"video_text": "No video provided for analysis."}

    # Get OpenRouter client
    client = get_openrouter_client(configuration)

    # Prepare headers for OpenRouter
    extra_headers = {}
    if configuration.openrouter_site_url:
        extra_headers["HTTP-Referer"] = configuration.openrouter_site_url
    if configuration.openrouter_site_name:
        extra_headers["X-Title"] = configuration.openrouter_site_name

    # Note: OpenRouter models may not have native video analysis capabilities like Gemini
    # We'll provide a text-based analysis request instead
    video_prompt = f"""
    I have a YouTube video at this URL: {video_url}

    The video is related to the topic: {topic}

    Since I cannot directly analyze the video content, please provide a general analysis framework for this topic that would typically be covered in educational or informational videos about "{topic}".

    Please include:
    1. Key concepts that are usually explained
    2. Common examples or case studies
    3. Important points that experts typically emphasize
    4. Practical applications or implications

    Note: This is a placeholder analysis since direct video processing is not available through OpenRouter.
    """

    video_response = client.chat.completions.create(
        extra_headers=extra_headers,
        model=configuration.video_model,
        messages=[{"role": "user", "content": video_prompt}],
        temperature=0.3
    )

    video_text, _ = display_openrouter_response(video_response)

    return {"video_text": video_text}

@traceable(run_type="llm", name="Create Report", project_name="multi-modal-researcher")
def create_report_node(state: ResearchState, config: RunnableConfig) -> dict:
    """Node that creates a comprehensive research report"""
    configuration = Configuration.from_runnable_config(config)
    topic = state["topic"]
    search_text = state.get("search_text", "")
    video_text = state.get("video_text", "")
    search_sources_text = state.get("search_sources_text", "")
    video_url = state.get("video_url", "")
    
    report, synthesis_text = create_research_report(
        topic, search_text, video_text, search_sources_text, video_url, configuration
    )
    
    return {
        "report": report,
        "synthesis_text": synthesis_text
    }


@traceable(run_type="llm", name="Create Podcast", project_name="multi-modal-researcher")
def create_podcast_node(state: ResearchState, config: RunnableConfig) -> dict:
    """Node that creates a podcast discussion"""
    configuration = Configuration.from_runnable_config(config)
    topic = state["topic"]
    search_text = state.get("search_text", "")
    video_text = state.get("video_text", "")
    search_sources_text = state.get("search_sources_text", "")
    video_url = state.get("video_url", "")
    
    # Create unique filename based on topic
    safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"research_podcast_{safe_topic.replace(' ', '_')}.wav"
    
    podcast_script, podcast_filename = create_podcast_discussion(
        topic, search_text, video_text, search_sources_text, video_url, filename, configuration
    )
    
    return {
        "podcast_script": podcast_script,
        "podcast_filename": podcast_filename
    }


def should_analyze_video(state: ResearchState) -> str:
    """Conditional edge to determine if video analysis should be performed"""
    if state.get("video_url"):
        return "analyze_video"
    else:
        return "create_report"


def create_research_graph() -> StateGraph:
    """Create and return the research workflow graph"""
    
    # Create the graph with configuration schema
    graph = StateGraph(
        ResearchState, 
        input=ResearchStateInput, 
        output=ResearchStateOutput,
        config_schema=Configuration
    )
    
    # Add nodes
    graph.add_node("search_research", search_research_node)
    graph.add_node("analyze_video", analyze_video_node)
    graph.add_node("create_report", create_report_node)
    graph.add_node("create_podcast", create_podcast_node)
    
    # Add edges
    graph.add_edge(START, "search_research")
    graph.add_conditional_edges(
        "search_research",
        should_analyze_video,
        {
            "analyze_video": "analyze_video",
            "create_report": "create_report"
        }
    )
    graph.add_edge("analyze_video", "create_report")
    graph.add_edge("create_report", "create_podcast")
    graph.add_edge("create_podcast", END)
    
    return graph


def create_compiled_graph():
    """Create and compile the research graph"""
    graph = create_research_graph()
    return graph.compile()