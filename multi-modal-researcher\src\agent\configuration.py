"""Configuration settings for the research and podcast generation app"""

import os
from dataclasses import dataclass, fields
from typing import Optional, Any
from langchain_core.runnables import RunnableConfig


@dataclass(kw_only=True)
class Configuration:
    """LangGraph Configuration for the deep research agent."""

    # OpenRouter API settings
    openrouter_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    openrouter_site_url: str = ""  # Optional: for rankings on openrouter.ai
    openrouter_site_name: str = ""  # Optional: for rankings on openrouter.ai

    # Model settings - using OpenRouter model names
    search_model: str = "openai/gpt-4o"  # Default model for web search
    synthesis_model: str = "openai/gpt-4o"  # Model for report synthesis
    video_model: str = "openai/gpt-4o"  # Model for video analysis
    podcast_script_model: str = "openai/gpt-4o"  # Model for podcast script generation

    # Temperature settings for different use cases
    search_temperature: float = 0.0           # Factual search queries
    synthesis_temperature: float = 0.3        # Balanced synthesis
    podcast_script_temperature: float = 0.4   # Creative dialogue

    # 豆包 TTS Configuration (火山引擎)
    douban_appid: str = ""
    douban_token: str = ""
    douban_cluster: str = "volcano_tts"
    douban_host: str = "openspeech.bytedance.com"
    douban_api_url: str = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"

    # TTS Voice Configuration
    mike_voice: str = "zh_male_M392_conversation_wvae_bigtts"  # 豆包 voice for Mike
    sarah_voice: str = "zh_female_F001_conversation_wvae_bigtts"  # 豆包 voice for Dr. Sarah
    tts_encoding: str = "wav"  # Audio encoding format
    tts_speed_ratio: float = 1.0  # Speech speed
    tts_volume_ratio: float = 1.0  # Volume level
    tts_rate: int = 24000  # Sample rate
    tts_channels: int = 1  # Audio channels

    def __post_init__(self):
        """Load configuration from environment variables if not set"""
        # Load OpenRouter configuration
        if not self.openrouter_api_key:
            self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "")
        if not self.openrouter_site_url:
            self.openrouter_site_url = os.getenv("OPENROUTER_SITE_URL", "")
        if not self.openrouter_site_name:
            self.openrouter_site_name = os.getenv("OPENROUTER_SITE_NAME", "")

        # Load Douban TTS configuration
        if not self.douban_appid:
            self.douban_appid = os.getenv("DOUBAN_APPID", "")
        if not self.douban_token:
            self.douban_token = os.getenv("DOUBAN_TOKEN", "")

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})

