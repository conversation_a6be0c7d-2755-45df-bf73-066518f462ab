#!/usr/bin/env python3
"""
Simple web demo for Multi-Modal Researcher
"""

import os
import sys
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.configuration import Configuration

class DemoHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_html_page()
        elif parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/config':
            self.send_config()
        elif parsed_path.path == '/research':
            query_params = parse_qs(parsed_path.query)
            topic = query_params.get('topic', ['AI in Healthcare'])[0]
            video_url = query_params.get('video_url', [''])[0]
            self.send_research_result(topic, video_url)
        else:
            self.send_404()
    
    def send_html_page(self):
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Multi-Modal Researcher Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .feature { background: #e8f4fd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .demo-form { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
        input[type="text"] { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .status { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .endpoint { background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Modal Researcher Demo</h1>
        
        <div class="feature">
            <h3>🤖 OpenRouter API Integration</h3>
            <p>Access to multiple LLM models through a unified API for research and content generation.</p>
        </div>
        
        <div class="feature">
            <h3>🎙️ Douban TTS Integration</h3>
            <p>High-quality Chinese text-to-speech with WebSocket streaming for podcast generation.</p>
        </div>
        
        <div class="feature">
            <h3>📊 Multi-Modal Research</h3>
            <p>Combines text research with video context analysis for comprehensive reports.</p>
        </div>
        
        <div class="demo-form">
            <h3>🔬 Try Research Demo</h3>
            <form action="/research" method="get">
                <label>Research Topic:</label>
                <input type="text" name="topic" value="Artificial Intelligence in Healthcare" placeholder="Enter your research topic">
                
                <label>Video URL (optional):</label>
                <input type="text" name="video_url" placeholder="https://youtu.be/example" value="">
                
                <button type="submit">🔍 Start Research</button>
            </form>
        </div>
        
        <div class="status warning">
            <strong>⚠️ Demo Mode:</strong> Please configure your API keys in the .env file for full functionality.
        </div>
        
        <h3>📡 API Endpoints</h3>
        <div class="endpoint">GET /health - System health check</div>
        <div class="endpoint">GET /config - Current configuration</div>
        <div class="endpoint">GET /research?topic=YourTopic - Research demo</div>
        
        <h3>🔧 Configuration</h3>
        <p>To enable full functionality, configure your API keys in the <code>.env</code> file:</p>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
OPENROUTER_API_KEY=your_openrouter_api_key_here
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here
        </pre>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_health_check(self):
        try:
            config = Configuration()
            
            openrouter_configured = bool(config.openrouter_api_key and not config.openrouter_api_key.startswith("demo_"))
            douban_configured = bool(config.douban_appid and config.douban_token and 
                                   not config.douban_appid.startswith("demo_") and 
                                   not config.douban_token.startswith("demo_"))
            
            health_data = {
                "status": "healthy",
                "openrouter_configured": openrouter_configured,
                "douban_configured": douban_configured,
                "models": {
                    "search": config.search_model,
                    "synthesis": config.synthesis_model,
                    "video": config.video_model,
                    "podcast_script": config.podcast_script_model
                }
            }
        except Exception as e:
            health_data = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        self.send_json_response(health_data)
    
    def send_config(self):
        try:
            config = Configuration()
            config_data = {
                "openrouter_base_url": config.openrouter_base_url,
                "models": {
                    "search_model": config.search_model,
                    "synthesis_model": config.synthesis_model,
                    "video_model": config.video_model,
                    "podcast_script_model": config.podcast_script_model
                },
                "tts_settings": {
                    "encoding": config.tts_encoding,
                    "speed_ratio": config.tts_speed_ratio,
                    "volume_ratio": config.tts_volume_ratio,
                    "rate": config.tts_rate,
                    "mike_voice": config.mike_voice,
                    "sarah_voice": config.sarah_voice
                }
            }
            self.send_json_response(config_data)
        except Exception as e:
            self.send_json_response({"error": str(e)}, 500)
    
    def send_research_result(self, topic, video_url):
        try:
            config = Configuration()
            
            # Check if APIs are configured
            openrouter_configured = bool(config.openrouter_api_key and not config.openrouter_api_key.startswith("demo_"))
            douban_configured = bool(config.douban_appid and config.douban_token and 
                                   not config.douban_appid.startswith("demo_") and 
                                   not config.douban_token.startswith("demo_"))
            
            # Generate demo results
            if openrouter_configured:
                report = f"# Research Report: {topic}\n\n## Real API Integration\n\nThis would contain real research results from OpenRouter API."
                podcast_script = f"Real podcast script for {topic} would be generated here using OpenRouter API."
            else:
                report = f"# Research Report: {topic}\n\n## Demo Mode\n\nThis is a demo response. Configure your OpenRouter API key for real research capabilities.\n\nThe topic '{topic}' would be thoroughly analyzed using advanced language models."
                podcast_script = f"Demo podcast script for {topic}:\n\nMike: Welcome to our discussion about {topic}.\nDr. Sarah: Thank you. This is demo mode - please configure your API keys for full functionality."
            
            result = {
                "topic": topic,
                "video_url": video_url,
                "report": report,
                "podcast_script": podcast_script,
                "status": "success" if openrouter_configured else "demo",
                "openrouter_configured": openrouter_configured,
                "douban_configured": douban_configured
            }
            
            self.send_json_response(result)
            
        except Exception as e:
            self.send_json_response({"error": str(e)}, 500)
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())
    
    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>404 Not Found</h1>')
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

def start_server():
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, DemoHandler)
    print(f"🌐 Demo server running at http://127.0.0.1:8000")
    print(f"📚 API documentation available at endpoints")
    print(f"🔧 Configure API keys in .env file for full functionality")
    httpd.serve_forever()

if __name__ == "__main__":
    print("🚀 Starting Multi-Modal Researcher Web Demo")
    print("=" * 60)
    start_server()
