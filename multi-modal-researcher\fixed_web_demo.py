#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed web demo for Multi-Modal Researcher
解决乱码问题，修正为豆包 TTS API
"""

import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class DemoHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_html_page()
        elif parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/config':
            self.send_config()
        elif parsed_path.path == '/research':
            query_params = parse_qs(parsed_path.query)
            topic = query_params.get('topic', ['人工智能在医疗领域的应用'])[0]
            video_url = query_params.get('video_url', [''])[0]
            self.send_research_result(topic, video_url)
        else:
            self.send_404()
    
    def send_html_page(self):
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模态研究助手演示</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif; 
            margin: 40px; 
            background-color: #f5f5f5; 
            line-height: 1.6;
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px; 
            font-size: 2.2em;
        }
        .feature { 
            background: #e8f4fd; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 8px; 
            border-left: 4px solid #007cba; 
        }
        .demo-form { 
            background: #f9f9f9; 
            padding: 25px; 
            border-radius: 8px; 
            margin: 25px 0; 
        }
        input[type="text"] { 
            width: 100%; 
            padding: 12px; 
            margin: 8px 0; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            box-sizing: border-box; 
            font-size: 14px;
        }
        button { 
            background: #007cba; 
            color: white; 
            padding: 12px 25px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        button:hover { background: #005a87; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .endpoint { 
            background: #e9ecef; 
            padding: 12px; 
            margin: 8px 0; 
            border-radius: 4px; 
            font-family: 'Consolas', 'Monaco', monospace; 
            font-size: 13px;
        }
        .result-section { 
            background: #f8f9fa; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px; 
            border: 1px solid #dee2e6; 
        }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 13px;
        }
        .nav-links { text-align: center; margin: 20px 0; }
        .nav-links a { 
            margin: 0 15px; 
            color: #007cba; 
            text-decoration: none; 
            font-weight: bold; 
        }
        .nav-links a:hover { text-decoration: underline; }
        .highlight { color: #007cba; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 多模态研究助手演示</h1>
        
        <div class="nav-links">
            <a href="/">🏠 首页</a>
            <a href="/health">💚 健康检查</a>
            <a href="/config">⚙️ 配置信息</a>
            <a href="/research?topic=人工智能在医疗领域的应用">🔬 演示研究</a>
        </div>
        
        <div class="feature">
            <h3>🤖 OpenRouter API 集成</h3>
            <p>通过统一的 API 访问多种大语言模型（GPT-4、Claude、Gemini 等），用于研究和内容生成。支持灵活的模型选择，适用于不同任务需求。</p>
        </div>
        
        <div class="feature">
            <h3>🎙️ 豆包 TTS API 集成</h3>
            <p>集成火山引擎豆包 TTS API，提供高质量的中文语音合成服务。支持 WebSocket 流式传输，实现实时音频生成，适用于播客制作。</p>
        </div>
        
        <div class="feature">
            <h3>📊 多模态研究功能</h3>
            <p>结合文本研究与视频内容分析，生成综合性研究报告。支持结构化的 Markdown 报告生成，包含引用和执行摘要。</p>
        </div>
        
        <div class="demo-form">
            <h3>🔬 尝试研究演示</h3>
            <form action="/research" method="get">
                <label><strong>研究主题：</strong></label>
                <input type="text" name="topic" value="人工智能在医疗领域的应用" placeholder="请输入您的研究主题">
                
                <label><strong>视频链接（可选）：</strong></label>
                <input type="text" name="video_url" placeholder="https://youtu.be/example" value="">
                
                <button type="submit">🔍 开始研究</button>
            </form>
        </div>
        
        <div class="status warning">
            <strong>⚠️ 演示模式：</strong> 当前运行在演示模式。要启用完整的 AI 生成内容功能，请在 .env 文件中配置您的 API 密钥。
        </div>
        
        <div class="result-section">
            <h3>📡 可用 API 端点</h3>
            <div class="endpoint">GET / - 演示界面</div>
            <div class="endpoint">GET /health - 系统健康状态检查</div>
            <div class="endpoint">GET /config - 当前系统配置信息</div>
            <div class="endpoint">GET /research?topic=您的主题&video_url=可选视频链接 - 研究演示</div>
        </div>
        
        <div class="result-section">
            <h3>🔧 配置设置</h3>
            <p>要启用完整的 AI 生成内容功能，请在 <code>.env</code> 文件中配置您的 API 密钥：</p>
            <pre># OpenRouter API 配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_SITE_URL=https://your-site.com  # 可选
OPENROUTER_SITE_NAME=Your App Name  # 可选

# 豆包 TTS API 配置  
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here</pre>
            
            <p><strong>获取 API 密钥：</strong></p>
            <ul>
                <li><strong>OpenRouter：</strong> 在 <a href="https://openrouter.ai" target="_blank" class="highlight">openrouter.ai</a> 注册并获取 API 密钥</li>
                <li><strong>豆包 TTS：</strong> 在 <a href="https://www.volcengine.com/docs/6561/1257584" target="_blank" class="highlight">火山引擎</a> 申请访问权限</li>
            </ul>
        </div>
        
        <div class="result-section">
            <h3>🎯 核心功能</h3>
            <ul>
                <li><strong>灵活模型选择：</strong> 支持 GPT-4、Claude、Gemini 等多种模型</li>
                <li><strong>研究报告生成：</strong> 全面的主题分析和报告创建</li>
                <li><strong>视频内容分析：</strong> 上下文感知的视频内容分析</li>
                <li><strong>播客制作：</strong> 自然对话生成与 TTS 语音合成</li>
                <li><strong>中文优化：</strong> 针对中文内容优化，集成豆包 TTS API</li>
            </ul>
        </div>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_health_check(self):
        # 检查环境变量
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")

        openrouter_configured = bool(openrouter_key and not openrouter_key.startswith("demo_"))
        douban_configured = bool(douban_appid and douban_token and
                               not douban_appid.startswith("demo_") and
                               not douban_token.startswith("demo_"))

        health_data = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "apis": {
                "openrouter": {
                    "configured": openrouter_configured,
                    "status": "ready" if openrouter_configured else "needs_configuration"
                },
                "douban_tts": {
                    "name": "豆包 TTS API",
                    "configured": douban_configured,
                    "status": "ready" if douban_configured else "needs_configuration"
                }
            },
            "models": {
                "search": "openai/gpt-4o",
                "synthesis": "openai/gpt-4o",
                "video": "openai/gpt-4o",
                "podcast_script": "openai/gpt-4o"
            },
            "features": {
                "research_generation": True,
                "video_analysis": True,
                "podcast_creation": True,
                "tts_synthesis": douban_configured
            }
        }

        self.send_json_response(health_data)

    def send_config(self):
        config_data = {
            "system": {
                "name": "多模态研究助手",
                "version": "1.0.0",
                "mode": "demo" if not os.getenv("OPENROUTER_API_KEY", "").replace("demo_", "") else "production"
            },
            "openrouter": {
                "base_url": "https://openrouter.ai/api/v1",
                "configured": bool(os.getenv("OPENROUTER_API_KEY", "").replace("demo_", "")),
                "models": {
                    "search_model": "openai/gpt-4o",
                    "synthesis_model": "openai/gpt-4o",
                    "video_model": "openai/gpt-4o",
                    "podcast_script_model": "openai/gpt-4o"
                }
            },
            "douban_tts": {
                "name": "豆包 TTS API",
                "provider": "火山引擎",
                "cluster": "volcano_tts",
                "host": "openspeech.bytedance.com",
                "configured": bool(os.getenv("DOUBAN_APPID", "").replace("demo_", "") and
                                 os.getenv("DOUBAN_TOKEN", "").replace("demo_", "")),
                "settings": {
                    "encoding": "wav",
                    "speed_ratio": 1.0,
                    "volume_ratio": 1.0,
                    "rate": 24000,
                    "voices": {
                        "mike": "zh_male_M392_conversation_wvae_bigtts",
                        "sarah": "zh_female_F001_conversation_wvae_bigtts"
                    }
                }
            }
        }

        self.send_json_response(config_data)

    def send_research_result(self, topic, video_url):
        # 检查 API 是否已配置
        openrouter_configured = bool(os.getenv("OPENROUTER_API_KEY", "").replace("demo_", ""))
        douban_configured = bool(os.getenv("DOUBAN_APPID", "").replace("demo_", "") and
                               os.getenv("DOUBAN_TOKEN", "").replace("demo_", ""))

        # 生成演示结果
        if openrouter_configured:
            report = f"""# 研究报告：{topic}

## 执行摘要

[真实 OpenRouter API 集成已激活]

本研究报告将包含通过 OpenRouter API 的先进语言模型生成的综合分析。系统将分析主题"{topic}"，使用配置的模型提供详细见解、关键发现和专家分析。

## 主要发现

1. **核心洞察**：此处将显示真实的 AI 生成分析
2. **当前趋势**：该领域的最新发展和趋势
3. **专家观点**：来自多个来源的综合观点
4. **未来影响**：预测的发展和考虑因素

## 视频分析
{f"- **链接**：{video_url}" if video_url else "- 未提供视频进行分析"}
{f"- **内容分析**：将使用配置的模型执行真实的视频分析" if video_url else ""}

## 来源和引用
- 此处将收集和引用真实来源
- 将包含多个权威参考资料
- 引用将被正确格式化和验证

---
*报告使用 OpenRouter API 生成，API 密钥：{os.getenv("OPENROUTER_API_KEY", "")[:10]}...*
"""

            podcast_script = f"""真实播客脚本：{topic}

Mike：欢迎收听我们的研究讨论。今天我们探讨{topic}。Sarah 博士，这是一个引人入胜的研究。

Dr. Sarah：谢谢你，Mike。[此处将使用 OpenRouter API 提供真实的 AI 生成专家分析]

Mike：这个领域最重要的发展是什么？

Dr. Sarah：[此处将出现基于真实研究的全面 AI 生成回应]

Mike：您如何看待这对未来的影响？

Dr. Sarah：[此处将提供关于未来影响的专家 AI 生成见解]

Mike：谢谢您的见解，Sarah 博士。

Dr. Sarah：我的荣幸，Mike。这项研究显示出巨大的潜力。

[使用 OpenRouter API 生成 - 真实对话将更加详细和信息丰富]
"""
        else:
            report = f"""# 研究报告：{topic}

## 演示模式 - 需要配置

这是多模态研究助手系统的演示。要生成真实的 AI 驱动研究报告，请在 .env 文件中配置您的 OpenRouter API 密钥。

## 完整配置后您将获得：

1. **综合分析**：使用先进语言模型深入分析{topic}
2. **多源研究**：从多个权威来源收集信息
3. **专家综合**：AI 生成的专家级分析和见解
4. **当前趋势**：最新发展和新兴模式
5. **未来预测**：基于信息的未来发展预测

## 视频分析
{f"- **链接**：{video_url}" if video_url else "- 未提供视频进行分析"}
{f"- **分析**：将分析视频内容以获取关于{topic}的相关见解" if video_url else ""}

## 激活的演示功能：
✅ 系统架构和工作流程
✅ API 集成框架
✅ 配置管理
✅ 报告生成结构
✅ 播客脚本创建

## 启用完整功能：
1. 从 https://openrouter.ai 获取 OpenRouter API 密钥
2. 从火山引擎获取豆包 TTS 凭据
3. 使用 API 密钥配置您的 .env 文件
4. 重启系统

---
*演示模式 - 配置 API 密钥以获得完整的 AI 驱动功能*
"""

            podcast_script = f"""演示播客脚本：{topic}

Mike：欢迎收听我们的研究讨论。今天我们探讨{topic}。Sarah 博士，您能给我们一个概述吗？

Dr. Sarah：谢谢你，Mike。这目前运行在演示模式。要获得真实的 AI 生成专家分析，请配置您的 OpenRouter API 密钥。

Mike：在完整功能下，听众会了解到这个主题的什么内容？

Dr. Sarah：配置 OpenRouter API 后，我将提供关于{topic}的全面见解，包括当前研究、专家观点和未来影响。系统支持多种先进模型，如 GPT-4、Claude 等。

Mike：音频生成方面呢？

Dr. Sarah：豆包 TTS 集成将提供高质量的中文语音合成，创建像这样的自然对话，但包含真实的专家内容。

Mike：用户如何开始？

Dr. Sarah：只需在 .env 文件中配置 API 密钥，系统就会提供完整的 AI 驱动研究和播客生成功能。

Mike：谢谢您的概述，Sarah 博士。

Dr. Sarah：我的荣幸，Mike。配置这些 API 密钥以释放全部潜力！

---
[演示模式 - 配置 API 密钥以获得真实的 AI 生成内容]
"""

        result = {
            "topic": topic,
            "video_url": video_url or "未提供",
            "report": report,
            "podcast_script": podcast_script,
            "status": "success" if openrouter_configured else "demo",
            "configuration": {
                "openrouter_configured": openrouter_configured,
                "douban_configured": douban_configured,
                "mode": "production" if openrouter_configured else "demo"
            },
            "next_steps": [
                "配置 OpenRouter API 密钥以获得真实的 AI 研究",
                "配置豆包 TTS 凭据以进行语音合成",
                "使用您自己的研究主题进行测试",
                "通过 OpenRouter 探索不同的模型选项"
            ] if not openrouter_configured else [
                "系统已完全配置并准备就绪",
                "尝试不同的研究主题",
                "实验视频 URL 分析",
                "使用 TTS 生成播客"
            ]
        }

        self.send_json_response(result)

    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))

    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>404 - 页面未找到</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; text-align: center; margin-top: 100px; }
        h1 { color: #333; }
        a { color: #007cba; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>404 - 页面未找到</h1>
    <p>请求的资源不存在。</p>
    <p><a href="/">返回首页</a></p>
</body>
</html>"""
        self.wfile.write(html.encode('utf-8'))

    def log_message(self, format, *args):
        # 自定义日志记录
        print(f"[{self.address_string()}] {format % args}")

def start_server():
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, DemoHandler)
    print("🚀 多模态研究助手 Web 演示")
    print("=" * 60)
    print(f"🌐 服务器运行地址：http://127.0.0.1:8000")
    print(f"📚 可用 API 端点：")
    print(f"  - 健康检查：http://127.0.0.1:8000/health")
    print(f"  - 配置信息：http://127.0.0.1:8000/config")
    print(f"  - 研究演示：http://127.0.0.1:8000/research?topic=您的主题")
    print("=" * 60)
    print("🔧 在 .env 文件中配置 API 密钥以获得完整功能")
    print("⚡ 按 Ctrl+C 停止服务器")
    print("=" * 60)

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已被用户停止")
        httpd.server_close()

if __name__ == "__main__":
    start_server()
