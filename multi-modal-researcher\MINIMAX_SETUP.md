# MiniMax TTS API 集成指南

## 📋 概述

本项目已从豆包 TTS API 迁移到 MiniMax TTS API，提供更稳定和高质量的语音合成服务。

## 🔧 配置步骤

### 1. 获取 MiniMax API 密钥

1. 访问 [MiniMax 开放平台](https://platform.minimaxi.com/)
2. 注册账号并完成实名认证
3. 创建应用并获取 API Key
4. 记录您的 API 密钥

### 2. 配置环境变量

在 `.env` 文件中添加以下配置：

```bash
# MiniMax TTS API Configuration
MINIMAX_API_KEY=your_actual_api_key_here
MINIMAX_API_HOST=https://api.minimax.chat
```

### 3. 安装依赖

运行安装脚本：

```bash
python install_minimax.py
```

或手动安装：

```bash
pip install requests asyncio pathlib
```

## 🎵 支持的声音类型

### 系统预设声音

- **男声**:
  - `male-qn-qingse` - 清晰男声（推荐用于 Mike）
  - `male-qn-jingying` - 精英男声
  - `male-qn-badao` - 霸道男声

- **女声**:
  - `female-shaonv` - 少女声（推荐用于 Dr. Sarah）
  - `female-yujie` - 御姐声
  - `audiobook_female_1` - 有声书女声

### 声音参数

- **速度 (speed)**: 0.5 - 2.0，默认 1.0
- **音量 (volume)**: 0 - 10，默认 1.0
- **音调 (pitch)**: -12 - 12，默认 0
- **情感 (emotion)**: happy, sad, angry, fearful, disgusted, surprised, neutral

## 🚀 使用方法

### 基本语音合成

```python
from agent.minimax_tts import create_minimax_tts_client
from agent.configuration import Configuration

config = Configuration()
client = create_minimax_tts_client(config)

# 异步合成
await client.synthesize_text(
    text="你好，这是测试文本",
    voice_type="male-qn-qingse",
    output_file="test.wav"
)
```

### 多人对话合成

```python
dialogue = """Mike: 欢迎收听我们的播客！
Dr. Sarah: 谢谢Mike！今天我们来聊聊AI技术。
Mike: 那么Sarah博士，您能介绍一下这个话题吗？
Dr. Sarah: 当然可以。AI技术正在快速发展..."""

await client.synthesize_dialogue(
    dialogue_text=dialogue,
    mike_voice="male-qn-qingse",
    sarah_voice="female-shaonv",
    output_file="podcast.wav"
)
```

## 🔍 故障排除

### 常见错误

1. **API 密钥错误**
   - 检查 `.env` 文件中的 `MINIMAX_API_KEY` 是否正确
   - 确认 API 密钥有效且有足够余额

2. **网络连接问题**
   - 检查网络连接
   - 确认 `MINIMAX_API_HOST` 地址正确

3. **音频文件为空**
   - 检查文本内容是否过长（建议每段 < 500 字符）
   - 确认选择的声音类型存在

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 API 限制

- **文本长度**: 建议每次请求 < 500 字符
- **并发请求**: 根据账户等级限制
- **音频格式**: 支持 WAV, MP3, PCM
- **采样率**: 8000, 16000, 22050, 24000, 32000, 44100 Hz

## 🎯 最佳实践

1. **文本分割**: 长文本自动分割为小段
2. **错误处理**: 实现重试机制
3. **缓存策略**: 避免重复合成相同内容
4. **音频质量**: 使用 32000 Hz 采样率获得最佳质量

## 📞 技术支持

如遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看 MiniMax 官方文档
3. 在项目 Issues 中报告问题

## 🔄 从豆包 TTS 迁移

原有的接口保持兼容：

- `synthesize_dialogue_sync()` 函数接口不变
- 音频文件输出格式保持 WAV
- Web 界面播放功能无需修改

主要变化：

- 配置文件从 `DOUBAN_*` 改为 `MINIMAX_*`
- 声音类型名称更新
- API 调用方式从 WebSocket 改为 HTTP REST
