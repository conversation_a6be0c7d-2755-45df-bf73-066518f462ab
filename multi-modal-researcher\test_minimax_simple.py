#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 MiniMax TTS 测试
"""

import os
import sys
import asyncio
sys.path.insert(0, 'src')

# 手动加载环境变量
def load_env():
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

load_env()

try:
    from agent.configuration import Configuration
    from agent.minimax_tts import create_minimax_tts_client
    
    config = Configuration()
    print('✅ 配置加载成功')
    print('MiniMax API Key:', config.minimax_api_key[:20] + '...' if config.minimax_api_key else 'None')
    print('MiniMax API Host:', config.minimax_api_host)
    print('Mike Voice:', config.mike_voice)
    print('Sarah Voice:', config.sarah_voice)
    
    # 创建 TTS 客户端
    client = create_minimax_tts_client(config)
    print('✅ MiniMax TTS 客户端创建成功')
    
    # 测试简单的文本合成
    async def test_simple_tts():
        print('🎵 测试简单文本合成...')
        
        result = await client.synthesize_text(
            text='你好，这是 MiniMax TTS API 的测试。',
            voice_type=config.mike_voice,
            output_file='test_minimax_simple.wav'
        )
        
        if result:
            print('✅ 简单文本合成成功！')
            # 检查文件大小
            if os.path.exists('test_minimax_simple.wav'):
                size = os.path.getsize('test_minimax_simple.wav')
                print(f'音频文件大小: {size} 字节')
            else:
                print('❌ 音频文件未生成')
        else:
            print('❌ 简单文本合成失败')
        
        return result
    
    # 测试多人对话合成
    async def test_dialogue_tts():
        print('\n🎙️ 测试多人对话合成...')
        
        dialogue = """Mike: 欢迎收听我们的播客！今天我们来测试 MiniMax TTS。
Dr. Sarah: 谢谢Mike！MiniMax TTS 确实是一个很棒的语音合成服务。
Mike: 那么Sarah博士，您觉得这个 API 怎么样？
Dr. Sarah: 我认为它的音质很好，而且支持多种声音类型。"""
        
        result = await client.synthesize_dialogue(
            dialogue_text=dialogue,
            mike_voice=config.mike_voice,
            sarah_voice=config.sarah_voice,
            output_file='test_minimax_dialogue.wav'
        )
        
        if result:
            print('✅ 多人对话合成成功！')
            # 检查文件大小
            if os.path.exists('test_minimax_dialogue.wav'):
                size = os.path.getsize('test_minimax_dialogue.wav')
                print(f'对话音频文件大小: {size} 字节')
            else:
                print('❌ 对话音频文件未生成')
        else:
            print('❌ 多人对话合成失败')
        
        return result
    
    # 运行测试
    async def run_tests():
        print('🚀 开始 MiniMax TTS 测试...\n')
        
        # 测试1：简单文本合成
        simple_result = await test_simple_tts()
        
        # 测试2：多人对话合成
        dialogue_result = await test_dialogue_tts()
        
        print('\n📊 测试结果总结:')
        print(f'简单文本合成: {"✅ 成功" if simple_result else "❌ 失败"}')
        print(f'多人对话合成: {"✅ 成功" if dialogue_result else "❌ 失败"}')
        
        if simple_result and dialogue_result:
            print('\n🎉 所有测试通过！MiniMax TTS 集成成功！')
        else:
            print('\n⚠️ 部分测试失败，请检查配置和网络连接。')
    
    # 运行异步测试
    asyncio.run(run_tests())
    
except Exception as e:
    print('❌ 测试失败:', str(e))
    import traceback
    traceback.print_exc()
