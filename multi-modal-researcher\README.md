# Multi-Modal Researcher

This project is a research and podcast generation workflow that uses LangGraph with OpenRouter API for LLM operations and Douban TTS for high-quality Chinese voice synthesis. You can pass a research topic and, optionally, a YouTube video URL. The system will then perform research on the topic, analyze the video context, combine the insights, and generate a comprehensive report as well as a podcast discussion on the topic.

Key features:
- 🤖 **OpenRouter API Integration**: Access to multiple LLM models through a unified API
- 🎙️ **Douban TTS**: High-quality Chinese text-to-speech with WebSocket streaming
- 📊 **Multi-modal Research**: Combines text research with video context analysis
- 📝 **Comprehensive Reports**: Generates detailed markdown reports with citations
- 🎧 **Podcast Generation**: Creates natural dialogue between expert and interviewer

![mutli-modal-researcher](https://github.com/user-attachments/assets/85067de9-3c36-47b8-ae06-29b00746036f)

## Quick Start

### Prerequisites

- Python 3.11+
- [uv](https://docs.astral.sh/uv/) package manager
- OpenRouter API key
- Douban TTS API credentials (appid and token)

### Setup

1. **<PERSON>lone and navigate to the project**:
```bash
git clone https://github.com/langchain-ai/multi-modal-researcher
cd mutli-modal-researcher
```

2. **Set up environment variables**:
```bash
cp .env.example .env
```
Edit `.env` and add your API credentials:
```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_SITE_URL=your_site_url_here  # Optional
OPENROUTER_SITE_NAME=your_site_name_here  # Optional

# Douban TTS API Configuration
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here
```

**Getting API Keys:**
- **OpenRouter**: Sign up at [openrouter.ai](https://openrouter.ai) and get your API key
- **Douban TTS**: Apply for access at [Volcano Engine](https://www.volcengine.com/docs/6561/1257584)

3. **Run the development server**:

```bash
# Install uv package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
# Install dependencies and start the LangGraph server
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev --allow-blocking
```

4. **Access the application**:

LangGraph will open in your browser.

```bash
╦  ┌─┐┌┐┌┌─┐╔═╗┬─┐┌─┐┌─┐┬ ┬
║  ├─┤││││ ┬║ ╦├┬┘├─┤├─┘├─┤
╩═╝┴ ┴┘└┘└─┘╚═╝┴└─┴ ┴┴  ┴ ┴

- 🚀 API: http://127.0.0.1:2024
- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
- 📚 API Docs: http://127.0.0.1:2024/docs
```

5. Pass a `topic` and optionally a `video_url`.

Example:
* `topic`: Give me an overview of the idea that LLMs are like a new kind of operating system.
* `video_url`: https://youtu.be/LCEmiRjPEtQ?si=raeMN2Roy5pESNG2

<img width="1604" alt="Screenshot 2025-06-24 at 5 13 31 PM" src="https://github.com/user-attachments/assets/6407e802-8932-4cfb-bdf9-5af96050ee1f" />

Result:

[🔍 See the example report](./example/report/karpathy_os.md)

[▶️ Download the example podcast](./example/audio/karpathy_os.wav)

## Architecture

The system implements a LangGraph workflow with the following nodes:

1. **Search Research Node**: Performs web search using Gemini's Google Search integration
2. **Analyze Video Node**: Analyzes YouTube videos when provided (conditional)
3. **Create Report Node**: Synthesizes findings into a comprehensive markdown report
4. **Create Podcast Node**: Generates a 2-speaker podcast discussion with TTS audio

### Workflow

```
START → search_research → [analyze_video?] → create_report → create_podcast → END
```

The workflow conditionally includes video analysis if a YouTube URL is provided, otherwise proceeds directly to report generation.

### Output

The system generates:

- **Research Report**: Comprehensive markdown report with executive summary and sources
- **Podcast Script**: Natural dialogue between Dr. Sarah (expert) and Mike (interviewer)  
- **Audio File**: Multi-speaker TTS audio file (`research_podcast_*.wav`)

## Configuration

The system supports runtime configuration through the `Configuration` class:

### OpenRouter Model Settings
- `search_model`: Model for web search (default: "openai/gpt-4o")
- `synthesis_model`: Model for report synthesis (default: "openai/gpt-4o")
- `video_model`: Model for video analysis (default: "openai/gpt-4o")
- `podcast_script_model`: Model for podcast script generation (default: "openai/gpt-4o")

### Temperature Settings
- `search_temperature`: Factual search queries (default: 0.0)
- `synthesis_temperature`: Balanced synthesis (default: 0.3)
- `podcast_script_temperature`: Creative dialogue (default: 0.4)

### Douban TTS Settings
- `mike_voice`: Voice for interviewer (default: "zh_male_M392_conversation_wvae_bigtts")
- `sarah_voice`: Voice for expert (default: "zh_female_F001_conversation_wvae_bigtts")
- `tts_encoding`: Audio format (default: "wav")
- `tts_speed_ratio`: Speech speed (default: 1.0)
- `tts_volume_ratio`: Volume level (default: 1.0)
- Audio quality and WebSocket streaming settings

## Project Structure

```
├── src/agent/
│   ├── state.py           # State definitions (input/output schemas)
│   ├── configuration.py   # Runtime configuration class
│   ├── utils.py          # Utility functions (TTS, report generation)
│   └── graph.py          # LangGraph workflow definition
├── langgraph.json        # LangGraph deployment configuration
├── pyproject.toml        # Python package configuration
└── .env                  # Environment variables
```

## Key Components

### State Management

- **ResearchStateInput**: Input schema (topic, optional video_url)
- **ResearchStateOutput**: Output schema (report, podcast_script, podcast_filename)
- **ResearchState**: Complete state including intermediate results

### Utility Functions

- **display_openrouter_response()**: Processes OpenRouter API responses and displays as markdown
- **create_podcast_discussion()**: Generates scripted dialogue using OpenRouter and TTS audio using Douban
- **create_research_report()**: Synthesizes multi-modal research into comprehensive reports
- **get_openrouter_client()**: Creates configured OpenRouter client
- **DoubanTTSClient**: WebSocket-based TTS client for high-quality voice synthesis

## Deployment

The application is configured for deployment on:

- **Local Development**: Using LangGraph CLI with in-memory storage
- **LangGraph Platform**: Production deployment with persistent storage
- **Self-Hosted**: Using Docker containers

## Dependencies

Core dependencies managed via `pyproject.toml`:

- `langgraph>=0.2.6` - Workflow orchestration
- `openai>=1.0.0` - OpenRouter API client (OpenAI-compatible)
- `websockets>=12.0` - WebSocket support for Douban TTS
- `langchain>=0.3.19` - LangChain integrations
- `rich` - Enhanced terminal output
- `python-dotenv` - Environment management

## License

MIT License - see LICENSE file for details.
