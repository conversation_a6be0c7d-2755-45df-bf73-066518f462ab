#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播客生成功能
"""

import os
import sys
sys.path.insert(0, 'src')

# 手动加载环境变量
def load_env():
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

load_env()

try:
    from agent.configuration import Configuration
    from agent.utils import create_podcast_discussion
    
    config = Configuration()
    print('✅ 配置加载成功')
    print('DOUBAN_APPID:', config.douban_appid)
    print('DOUBAN_TOKEN:', config.douban_token[:10] + '...' if config.douban_token else 'None')
    
    # 测试简单的多人对话合成
    from agent.douban_tts import create_douban_tts_client

    print('🎙️ 测试多人对话合成...')

    # 创建简单的对话文本
    dialogue_text = """Mike: 欢迎收听我们的播客！今天我们来聊聊AI语音合成技术。
Dr. Sarah: 谢谢Mike！AI语音合成确实是一个非常有趣的话题。
Mike: 那么Sarah博士，您能简单介绍一下这项技术吗？
Dr. Sarah: 当然可以。语音合成技术正在快速发展，为各种应用提供了新的可能性。
Mike: 听起来很棒！这项技术有哪些实际应用呢？
Dr. Sarah: 它可以用于智能助手、有声读物、无障碍服务等多个领域。"""

    # 测试多人对话合成
    client = create_douban_tts_client(config)

    import asyncio

    async def test_dialogue():
        result = await client.synthesize_dialogue(
            dialogue_text=dialogue_text,
            mike_voice=config.mike_voice,
            sarah_voice=config.sarah_voice,
            output_file='test_dialogue.wav'
        )
        return result

    success = asyncio.run(test_dialogue())

    if success:
        print('✅ 多人对话合成成功！')
        # 检查文件大小
        if os.path.exists('test_dialogue.wav'):
            size = os.path.getsize('test_dialogue.wav')
            print(f'对话音频文件大小: {size} 字节')
        else:
            print('❌ 对话音频文件未生成')
    else:
        print('❌ 多人对话合成失败')

    print('\n' + '='*50 + '\n')

    # 测试完整播客生成
    topic = 'AI语音合成测试'
    search_text = '语音合成技术正在快速发展，为各种应用提供了新的可能性。'
    video_text = '视频分析显示，语音合成在用户体验方面有显著改善。'
    search_sources_text = '来源：技术研究报告'
    video_url = ''
    filename = 'test_podcast_full.wav'
    
    print('🎙️ 开始生成播客...')
    podcast_script, podcast_filename = create_podcast_discussion(
        topic=topic,
        search_text=search_text,
        video_text=video_text,
        search_sources_text=search_sources_text,
        video_url=video_url,
        filename=filename,
        configuration=config
    )
    
    print('✅ 播客生成完成！')
    print('脚本长度:', len(podcast_script), '字符')
    print('音频文件:', podcast_filename)
    
    # 检查文件大小
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        print(f'音频文件大小: {size} 字节')
        if size > 0:
            print('✅ 音频文件生成成功！')
        else:
            print('❌ 音频文件为空')
    else:
        print('❌ 音频文件未生成')
    
except Exception as e:
    print('❌ 测试失败:', str(e))
    import traceback
    traceback.print_exc()
