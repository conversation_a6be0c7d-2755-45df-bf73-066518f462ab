#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播客生成功能
"""

import os
import sys
sys.path.insert(0, 'src')

# 手动加载环境变量
def load_env():
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

load_env()

try:
    from agent.configuration import Configuration
    from agent.utils import create_podcast_discussion
    
    config = Configuration()
    print('✅ 配置加载成功')
    print('DOUBAN_APPID:', config.douban_appid)
    print('DOUBAN_TOKEN:', config.douban_token[:10] + '...' if config.douban_token else 'None')
    
    # 测试播客生成
    topic = 'AI语音合成测试'
    search_text = '语音合成技术正在快速发展，为各种应用提供了新的可能性。'
    video_text = '视频分析显示，语音合成在用户体验方面有显著改善。'
    search_sources_text = '来源：技术研究报告'
    video_url = ''
    filename = 'test_podcast_full.wav'
    
    print('🎙️ 开始生成播客...')
    podcast_script, podcast_filename = create_podcast_discussion(
        topic=topic,
        search_text=search_text,
        video_text=video_text,
        search_sources_text=search_sources_text,
        video_url=video_url,
        filename=filename,
        configuration=config
    )
    
    print('✅ 播客生成完成！')
    print('脚本长度:', len(podcast_script), '字符')
    print('音频文件:', podcast_filename)
    
    # 检查文件大小
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        print(f'音频文件大小: {size} 字节')
        if size > 0:
            print('✅ 音频文件生成成功！')
        else:
            print('❌ 音频文件为空')
    else:
        print('❌ 音频文件未生成')
    
except Exception as e:
    print('❌ 测试失败:', str(e))
    import traceback
    traceback.print_exc()
