# Usage Guide: Multi-Modal Researcher with OpenRouter & Douban TTS

This guide explains how to use the updated Multi-Modal Researcher system with OpenRouter API and Douban TTS integration.

## Quick Start

### 1. Environment Setup

First, copy the environment template and configure your API credentials:

```bash
cp .env.example .env
```

Edit the `.env` file with your credentials:

```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_SITE_URL=https://your-site.com  # Optional
OPENROUTER_SITE_NAME=Your App Name  # Optional

# Douban TTS API Configuration
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here
```

### 2. Install Dependencies

```bash
# Install uv package manager if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install project dependencies
uv sync
```

### 3. Test Integrations

Run the integration test script to verify your setup:

```bash
python test_integrations.py
```

This will test:
- Configuration loading
- OpenRouter API connection
- Douban TTS API connection

### 4. Start the Application

```bash
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev --allow-blocking
```

The application will be available at:
- 🚀 API: http://127.0.0.1:2024
- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
- 📚 API Docs: http://127.0.0.1:2024/docs

## Configuration Options

### Model Selection

You can configure which models to use for different tasks by setting environment variables or modifying the configuration:

```bash
# Example: Use different models for different tasks
SEARCH_MODEL=anthropic/claude-3-haiku
SYNTHESIS_MODEL=openai/gpt-4o
VIDEO_MODEL=openai/gpt-4o-mini
PODCAST_SCRIPT_MODEL=anthropic/claude-3-sonnet
```

Available models depend on your OpenRouter subscription. Check [OpenRouter Models](https://openrouter.ai/models) for the full list.

### TTS Voice Configuration

Configure Douban TTS voices:

```bash
# Voice types for different speakers
MIKE_VOICE=zh_male_M392_conversation_wvae_bigtts
SARAH_VOICE=zh_female_F001_conversation_wvae_bigtts

# Audio settings
TTS_ENCODING=wav
TTS_SPEED_RATIO=1.0
TTS_VOLUME_RATIO=1.0
TTS_RATE=24000
```

## API Usage

### Basic Research Request

```json
{
  "topic": "Artificial Intelligence in Healthcare",
  "video_url": "https://youtu.be/example_video_id"
}
```

### Response Format

The system returns:

```json
{
  "report": "# Research Report: Artificial Intelligence in Healthcare\n\n## Executive Summary\n...",
  "podcast_script": "Mike: Welcome to our discussion about AI in healthcare...",
  "podcast_filename": "research_podcast_Artificial_Intelligence_in_Healthcare.wav"
}
```

## Features & Capabilities

### 1. Research Generation
- Uses OpenRouter API for flexible model selection
- Generates comprehensive research overviews
- Supports multiple LLM providers through OpenRouter

### 2. Video Analysis
- Provides contextual analysis framework for video content
- Note: Direct video processing requires additional integration
- Generates relevant discussion points based on topic

### 3. Report Synthesis
- Combines research and video insights
- Creates structured markdown reports
- Includes executive summaries and source citations

### 4. Podcast Generation
- Creates natural dialogue between Mike (interviewer) and Dr. Sarah (expert)
- Uses Douban TTS for high-quality Chinese voice synthesis
- Supports WebSocket streaming for real-time audio generation

## Troubleshooting

### Common Issues

1. **OpenRouter API Errors**
   - Check your API key is valid and has sufficient credits
   - Verify the model name is correct and available
   - Check rate limits and quotas

2. **Douban TTS Errors**
   - Ensure your appid and token are correct
   - Check your Douban TTS service status
   - Verify voice type names are valid

3. **Configuration Issues**
   - Run `python test_integrations.py` to diagnose problems
   - Check environment variable names and values
   - Ensure all required dependencies are installed

### Error Codes

- **OpenRouter API**: Check [OpenRouter Documentation](https://openrouter.ai/docs) for error codes
- **Douban TTS**: Refer to error codes in the [Douban TTS API Documentation](https://www.volcengine.com/docs/6561/1257584)

## Advanced Configuration

### Custom Model Configuration

Create a custom configuration by extending the Configuration class:

```python
from agent.configuration import Configuration

class CustomConfiguration(Configuration):
    search_model: str = "anthropic/claude-3-haiku"
    synthesis_model: str = "openai/gpt-4o"
    podcast_script_model: str = "anthropic/claude-3-sonnet"
```

### Custom TTS Voices

To use different Douban TTS voices, update the configuration:

```python
config = Configuration(
    mike_voice="zh_male_custom_voice",
    sarah_voice="zh_female_custom_voice"
)
```

## Performance Tips

1. **Model Selection**: Choose appropriate models for each task
   - Use faster models (like GPT-4o-mini) for simple tasks
   - Use more capable models (like GPT-4o) for complex analysis

2. **TTS Optimization**: 
   - Use WAV format for best quality
   - Adjust speed and volume ratios as needed
   - Consider caching for repeated text

3. **Rate Limiting**:
   - Monitor your OpenRouter usage
   - Implement appropriate delays if needed
   - Use batch processing for multiple requests

## Support

For issues and questions:
- Check the test script output for diagnostic information
- Review API documentation for both OpenRouter and Douban TTS
- Ensure all environment variables are properly configured
