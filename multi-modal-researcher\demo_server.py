#!/usr/bin/env python3
"""
Demo server for Multi-Modal Researcher with OpenRouter and Douban TTS
"""

import os
import sys
import asyncio
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import uvicorn

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.configuration import Configuration
from agent.utils import get_openrouter_client, display_openrouter_response, create_research_report, create_podcast_discussion

app = FastAPI(title="Multi-Modal Researcher Demo", version="1.0.0")

class ResearchRequest(BaseModel):
    topic: str
    video_url: Optional[str] = None

class ResearchResponse(BaseModel):
    report: str
    podcast_script: str
    podcast_filename: str
    status: str
    message: str

@app.get("/")
async def root():
    return {
        "message": "Multi-Modal Researcher API with OpenRouter and Douban TTS",
        "status": "running",
        "endpoints": {
            "research": "/research",
            "health": "/health",
            "config": "/config"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        config = Configuration()
        
        # Check if API keys are configured
        openrouter_configured = bool(config.openrouter_api_key or os.getenv("OPENROUTER_API_KEY"))
        douban_configured = bool(config.douban_appid or os.getenv("DOUBAN_APPID")) and bool(config.douban_token or os.getenv("DOUBAN_TOKEN"))
        
        return {
            "status": "healthy",
            "openrouter_configured": openrouter_configured,
            "douban_configured": douban_configured,
            "models": {
                "search": config.search_model,
                "synthesis": config.synthesis_model,
                "video": config.video_model,
                "podcast_script": config.podcast_script_model
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@app.get("/config")
async def get_config():
    """Get current configuration"""
    try:
        config = Configuration()
        return {
            "openrouter_base_url": config.openrouter_base_url,
            "models": {
                "search_model": config.search_model,
                "synthesis_model": config.synthesis_model,
                "video_model": config.video_model,
                "podcast_script_model": config.podcast_script_model
            },
            "tts_settings": {
                "encoding": config.tts_encoding,
                "speed_ratio": config.tts_speed_ratio,
                "volume_ratio": config.tts_volume_ratio,
                "rate": config.tts_rate,
                "mike_voice": config.mike_voice,
                "sarah_voice": config.sarah_voice
            },
            "douban_settings": {
                "cluster": config.douban_cluster,
                "host": config.douban_host
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/research", response_model=ResearchResponse)
async def conduct_research(request: ResearchRequest):
    """Conduct research and generate report and podcast"""
    try:
        config = Configuration()
        
        # Check API configuration
        api_key = config.openrouter_api_key or os.getenv("OPENROUTER_API_KEY")
        if not api_key or api_key.startswith("demo_"):
            return ResearchResponse(
                report="# Demo Mode\n\nPlease configure your OpenRouter API key to use this service.",
                podcast_script="Demo mode: Please configure your API keys.",
                podcast_filename="demo_mode.wav",
                status="demo",
                message="Demo mode - please configure real API keys"
            )
        
        # Simulate research process
        print(f"🔍 Starting research on topic: {request.topic}")
        
        # Step 1: Simulate search research
        search_text = f"Research findings about {request.topic}:\n\nThis is a simulated research result. In the real implementation, this would contain comprehensive research findings from the OpenRouter API."
        search_sources_text = "Simulated sources:\n1. Example Source 1\n2. Example Source 2"
        
        # Step 2: Simulate video analysis
        if request.video_url:
            video_text = f"Video analysis for {request.video_url}:\n\nThis is a simulated video analysis. The real implementation would analyze video content related to {request.topic}."
        else:
            video_text = "No video provided for analysis."
        
        # Step 3: Create research report
        print("📝 Generating research report...")
        try:
            report, synthesis_text = create_research_report(
                topic=request.topic,
                search_text=search_text,
                video_text=video_text,
                search_sources_text=search_sources_text,
                video_url=request.video_url or "",
                configuration=config
            )
        except Exception as e:
            print(f"Report generation failed: {e}")
            report = f"# Research Report: {request.topic}\n\n## Demo Mode\n\nReport generation failed in demo mode. Please configure your OpenRouter API key.\n\nError: {str(e)}"
        
        # Step 4: Create podcast discussion
        print("🎙️ Generating podcast script...")
        try:
            safe_topic = "".join(c for c in request.topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"research_podcast_{safe_topic.replace(' ', '_')}.wav"
            
            podcast_script, podcast_filename = create_podcast_discussion(
                topic=request.topic,
                search_text=search_text,
                video_text=video_text,
                search_sources_text=search_sources_text,
                video_url=request.video_url or "",
                filename=filename,
                configuration=config
            )
        except Exception as e:
            print(f"Podcast generation failed: {e}")
            podcast_script = f"Demo podcast script for {request.topic}:\n\nMike: Welcome to our discussion about {request.topic}.\nDr. Sarah: Thank you for having me. This is a demo mode response.\nMike: Could you tell us more about this topic?\nDr. Sarah: In demo mode, please configure your API keys for full functionality."
            podcast_filename = "demo_podcast.wav"
        
        print("✅ Research completed successfully!")
        
        return ResearchResponse(
            report=report,
            podcast_script=podcast_script,
            podcast_filename=podcast_filename,
            status="success",
            message="Research completed successfully"
        )
        
    except Exception as e:
        print(f"❌ Research failed: {e}")
        raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Multi-Modal Researcher Demo Server")
    print("=" * 60)
    print("📋 Features:")
    print("  - OpenRouter API integration for LLM operations")
    print("  - Douban TTS integration for voice synthesis")
    print("  - Multi-modal research and podcast generation")
    print("=" * 60)
    print("🌐 Server will be available at:")
    print("  - API: http://127.0.0.1:8000")
    print("  - Docs: http://127.0.0.1:8000/docs")
    print("  - Health: http://127.0.0.1:8000/health")
    print("=" * 60)
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
