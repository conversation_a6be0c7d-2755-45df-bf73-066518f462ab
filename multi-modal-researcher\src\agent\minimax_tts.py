"""
MiniMax TTS API integration
基于 MiniMax MCP 的文本转语音功能
"""

import os
import json
import wave
import asyncio
import requests
from typing import Optional, List, Tuple
from pathlib import Path


class MinimaxTTSClient:
    """MiniMax TTS API 客户端"""
    
    def __init__(self, api_key: str, api_host: str = "https://api.minimax.chat"):
        """
        初始化 MiniMax TTS 客户端
        
        Args:
            api_key: MiniMax API 密钥
            api_host: API 主机地址
        """
        self.api_key = api_key
        self.api_host = api_host
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'MM-API-Source': 'Multi-Modal-Researcher'
        })
    
    async def synthesize_text(self, text: str, voice_type: str, 
                            output_file: str, **kwargs) -> bool:
        """
        合成单段文本为语音
        
        Args:
            text: 要合成的文本
            voice_type: 声音类型
            output_file: 输出文件路径
            **kwargs: 其他参数
            
        Returns:
            bool: 合成是否成功
        """
        try:
            # 构建请求参数
            payload = {
                "model": kwargs.get("model", "speech-02-hd"),
                "text": text,
                "voice_setting": {
                    "voice_id": voice_type,
                    "speed": kwargs.get("speed", 1.0),
                    "vol": kwargs.get("volume", 1.0),
                    "pitch": kwargs.get("pitch", 0),
                    "emotion": kwargs.get("emotion", "happy")
                },
                "audio_setting": {
                    "sample_rate": kwargs.get("sample_rate", 32000),
                    "bitrate": kwargs.get("bitrate", 128000),
                    "format": "wav",  # 固定使用 WAV 格式
                    "channel": kwargs.get("channel", 1)
                },
                "language_boost": kwargs.get("language_boost", "auto")
            }
            
            print(f"Synthesizing text: {text[:50]}...")
            
            # 发送请求
            response = self.session.post(
                f"{self.api_host}/v1/t2a_v2",
                json=payload,
                timeout=30
            )
            
            # 检查响应
            response.raise_for_status()
            data = response.json()
            
            # 检查 API 响应状态
            base_resp = data.get("base_resp", {})
            if base_resp.get("status_code") != 0:
                print(f"API Error: {base_resp.get('status_msg')}")
                return False
            
            # 获取音频数据
            audio_hex = data.get('data', {}).get('audio', '')
            if not audio_hex:
                print("No audio data received")
                return False
            
            # 将十六进制转换为字节并保存
            audio_bytes = bytes.fromhex(audio_hex)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, "wb") as f:
                f.write(audio_bytes)
            
            print(f"Audio saved to: {output_file}")
            return True
            
        except Exception as e:
            print(f"TTS synthesis failed: {e}")
            return False
    
    async def synthesize_dialogue(self, dialogue_text: str, mike_voice: str, 
                                 sarah_voice: str, output_file: str) -> bool:
        """
        合成多人对话为语音
        1. 解析对话分离说话人
        2. 为每个说话人生成单独的音频文件
        3. 合并音频文件
        """
        
        try:
            # 解析对话
            segments = self.parse_dialogue(dialogue_text)
            if not segments:
                print("No dialogue segments found")
                return False
            
            # 生成每个片段的音频
            audio_segments = []
            temp_files = []
            
            for i, (speaker, text) in enumerate(segments):
                # 选择声音
                voice = mike_voice if speaker.lower() == 'mike' else sarah_voice
                
                # 创建临时文件
                temp_file = f"temp_segment_{i}_{speaker.lower()}.wav"
                temp_files.append(temp_file)
                
                print(f"Synthesizing {speaker}: {text[:50]}...")
                
                # 处理长文本分割
                if len(text) > 500:  # 限制每段文本长度
                    text_chunks = self.split_text(text, max_length=500)
                    chunk_files = []
                    
                    for j, chunk in enumerate(text_chunks):
                        chunk_file = f"temp_chunk_{i}_{j}_{speaker.lower()}.wav"
                        chunk_success = await self.synthesize_text(
                            text=chunk,
                            voice_type=voice,
                            output_file=chunk_file
                        )
                        if chunk_success:
                            chunk_files.append(chunk_file)
                    
                    # 合并该说话人的所有片段
                    if chunk_files:
                        success = self.combine_audio_segments(chunk_files, temp_file)
                        # 清理片段文件
                        for chunk_file in chunk_files:
                            try:
                                if os.path.exists(chunk_file):
                                    os.remove(chunk_file)
                            except:
                                pass
                    else:
                        success = False
                else:
                    # 直接合成
                    success = await self.synthesize_text(
                        text=text,
                        voice_type=voice,
                        output_file=temp_file
                    )
                
                if success and os.path.exists(temp_file):
                    audio_segments.append(temp_file)
                else:
                    print(f"Failed to synthesize segment for {speaker}")
            
            # 合并所有音频片段
            if audio_segments:
                success = self.combine_audio_segments(audio_segments, output_file)
                
                # 清理临时文件
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except:
                        pass
                
                return success
            else:
                print("No audio segments generated")
                return False
                
        except Exception as e:
            print(f"Dialogue synthesis failed: {e}")
            return False
    
    def parse_dialogue(self, dialogue_text: str) -> List[Tuple[str, str]]:
        """解析对话文本为 (说话人, 文本) 片段"""
        segments = []
        lines = dialogue_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 查找说话人模式如 "Mike:" 或 "Dr. Sarah:"
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    speaker = parts[0].strip()
                    text = parts[1].strip()
                    
                    # 标准化说话人名称
                    if 'mike' in speaker.lower():
                        speaker = 'Mike'
                    elif 'sarah' in speaker.lower() or 'dr.' in speaker.lower():
                        speaker = 'Dr. Sarah'
                    
                    if text:  # 只添加有实际文本的片段
                        segments.append((speaker, text))
        
        return segments
    
    def split_text(self, text: str, max_length: int = 500) -> List[str]:
        """将长文本分割为较小的片段"""
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        current_chunk = ""
        
        # 首先按句子分割
        sentences = text.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n')
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 如果添加这个句子会超过限制
            if len(current_chunk) + len(sentence) > max_length:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # 单个句子太长，按逗号分割
                    if len(sentence) > max_length:
                        parts = sentence.split('，')
                        for part in parts:
                            if len(current_chunk) + len(part) > max_length:
                                if current_chunk:
                                    chunks.append(current_chunk.strip())
                                current_chunk = part
                            else:
                                current_chunk += part + '，'
                    else:
                        current_chunk = sentence
            else:
                current_chunk += sentence
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def combine_audio_segments(self, audio_files: List[str], output_file: str) -> bool:
        """合并多个 WAV 文件为一个"""
        try:
            # 读取所有音频文件并合并
            combined_frames = []
            sample_rate = None
            channels = None
            sample_width = None
            
            for audio_file in audio_files:
                if not os.path.exists(audio_file):
                    print(f"Audio file not found: {audio_file}")
                    continue
                    
                with wave.open(audio_file, 'rb') as wav_file:
                    # 从第一个文件获取音频参数
                    if sample_rate is None:
                        sample_rate = wav_file.getframerate()
                        channels = wav_file.getnchannels()
                        sample_width = wav_file.getsampwidth()
                    
                    # 读取帧数据
                    frames = wav_file.readframes(wav_file.getnframes())
                    combined_frames.append(frames)
                    
                    # 在说话人之间添加短暂停顿（0.5秒）
                    pause_frames = int(sample_rate * 0.5) * channels * sample_width
                    combined_frames.append(b'\x00' * pause_frames)
            
            if not combined_frames:
                print("No audio frames to combine")
                return False
            
            # 写入合并后的音频
            with wave.open(output_file, 'wb') as output_wav:
                output_wav.setnchannels(channels)
                output_wav.setsampwidth(sample_width)
                output_wav.setframerate(sample_rate)
                
                for frames in combined_frames:
                    output_wav.writeframes(frames)
            
            print(f"Combined audio saved to: {output_file}")
            return True
            
        except Exception as e:
            print(f"Audio combination failed: {e}")
            return False


def synthesize_dialogue_sync(dialogue_text: str, mike_voice: str, 
                           sarah_voice: str, output_file: str, 
                           configuration) -> bool:
    """
    同步版本的对话合成函数，保持与原有接口兼容
    """
    try:
        client = create_minimax_tts_client(configuration)
        
        # 使用 asyncio 运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                client.synthesize_dialogue(dialogue_text, mike_voice, sarah_voice, output_file)
            )
            return result
        finally:
            loop.close()
            
    except Exception as e:
        print(f"Sync dialogue synthesis failed: {e}")
        return False


def create_minimax_tts_client(configuration) -> MinimaxTTSClient:
    """从配置创建 MiniMax TTS 客户端"""
    return MinimaxTTSClient(
        api_key=configuration.minimax_api_key,
        api_host=configuration.minimax_api_host
    )
