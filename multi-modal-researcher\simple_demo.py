#!/usr/bin/env python3
"""
Simple demo for Multi-Modal Researcher with OpenRouter and Douban TTS
"""

import os
import sys
import asyncio

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.configuration import Configuration
from agent.utils import get_openrouter_client, create_research_report, create_podcast_discussion

def print_banner():
    print("🚀 Multi-Modal Researcher Demo")
    print("=" * 60)
    print("📋 Features:")
    print("  - OpenRouter API integration for LLM operations")
    print("  - Douban TTS integration for voice synthesis")
    print("  - Multi-modal research and podcast generation")
    print("=" * 60)

def check_configuration():
    """Check if APIs are configured"""
    print("⚙️ Checking configuration...")
    
    try:
        config = Configuration()
        
        # Check OpenRouter API
        openrouter_key = config.openrouter_api_key or os.getenv("OPENROUTER_API_KEY")
        openrouter_configured = bool(openrouter_key and not openrouter_key.startswith("demo_"))
        
        # Check Douban TTS API
        douban_appid = config.douban_appid or os.getenv("DOUBAN_APPID")
        douban_token = config.douban_token or os.getenv("DOUBAN_TOKEN")
        douban_configured = bool(douban_appid and douban_token and 
                               not douban_appid.startswith("demo_") and 
                               not douban_token.startswith("demo_"))
        
        print(f"  OpenRouter API: {'✅ Configured' if openrouter_configured else '❌ Not configured (demo mode)'}")
        print(f"  Douban TTS API: {'✅ Configured' if douban_configured else '❌ Not configured (demo mode)'}")
        
        if not openrouter_configured:
            print("  📝 To configure OpenRouter: Set OPENROUTER_API_KEY in .env file")
        if not douban_configured:
            print("  📝 To configure Douban TTS: Set DOUBAN_APPID and DOUBAN_TOKEN in .env file")
        
        print(f"  Models configured:")
        print(f"    - Search: {config.search_model}")
        print(f"    - Synthesis: {config.synthesis_model}")
        print(f"    - Video: {config.video_model}")
        print(f"    - Podcast Script: {config.podcast_script_model}")
        
        return openrouter_configured, douban_configured, config
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False, False, None

def demo_research(topic, video_url=None):
    """Demonstrate research functionality"""
    print(f"\n🔍 Starting research on topic: '{topic}'")
    if video_url:
        print(f"📹 Video URL: {video_url}")
    
    openrouter_ok, douban_ok, config = check_configuration()
    
    if not config:
        print("❌ Cannot proceed without valid configuration")
        return
    
    try:
        # Step 1: Simulate search research
        print("\n📊 Step 1: Conducting web research...")
        if openrouter_ok:
            # In real implementation, this would use OpenRouter API
            search_text = f"[Real OpenRouter API would be used here]\n\nResearch findings about {topic}:\n\nThis would contain comprehensive research findings from the configured OpenRouter model."
        else:
            search_text = f"[Demo Mode]\n\nSimulated research findings about {topic}:\n\nThis is a demo response. Please configure your OpenRouter API key for real research capabilities."
        
        search_sources_text = "Demo sources:\n1. Example Source 1\n2. Example Source 2"
        print("✅ Web research completed")
        
        # Step 2: Video analysis
        print("\n📹 Step 2: Analyzing video content...")
        if video_url:
            if openrouter_ok:
                video_text = f"[Real OpenRouter API would be used here]\n\nVideo analysis for {video_url}:\n\nThis would contain actual video content analysis related to {topic}."
            else:
                video_text = f"[Demo Mode]\n\nSimulated video analysis for {video_url}:\n\nThis is a demo response. Please configure your OpenRouter API key for real video analysis."
        else:
            video_text = "No video provided for analysis."
        print("✅ Video analysis completed")
        
        # Step 3: Generate research report
        print("\n📝 Step 3: Generating research report...")
        try:
            if openrouter_ok:
                report, synthesis_text = create_research_report(
                    topic=topic,
                    search_text=search_text,
                    video_text=video_text,
                    search_sources_text=search_sources_text,
                    video_url=video_url or "",
                    configuration=config
                )
                print("✅ Research report generated using OpenRouter API")
            else:
                report = f"""# Research Report: {topic}

## Executive Summary

[Demo Mode] This is a simulated research report. To get real AI-generated reports, please configure your OpenRouter API key in the .env file.

The topic "{topic}" would be thoroughly analyzed using advanced language models through the OpenRouter API, providing comprehensive insights and analysis.

## Video Source
- **URL**: {video_url or "No video provided"}

## Additional Sources
{search_sources_text}

---
*Report generated in demo mode - configure OpenRouter API for full functionality*
"""
                print("⚠️ Research report generated in demo mode")
        except Exception as e:
            print(f"❌ Report generation failed: {e}")
            report = f"# Research Report: {topic}\n\n## Error\n\nReport generation failed: {str(e)}"
        
        # Step 4: Generate podcast
        print("\n🎙️ Step 4: Generating podcast script and audio...")
        try:
            safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"research_podcast_{safe_topic.replace(' ', '_')}.wav"
            
            if openrouter_ok:
                podcast_script, podcast_filename = create_podcast_discussion(
                    topic=topic,
                    search_text=search_text,
                    video_text=video_text,
                    search_sources_text=search_sources_text,
                    video_url=video_url or "",
                    filename=filename,
                    configuration=config
                )
                print("✅ Podcast script generated using OpenRouter API")
                if douban_ok:
                    print("✅ Audio synthesis attempted using Douban TTS")
                else:
                    print("⚠️ Audio synthesis skipped (Douban TTS not configured)")
            else:
                podcast_script = f"""Demo Podcast Script for: {topic}

Mike: Welcome to our research discussion. Today we're exploring {topic}. Dr. Sarah, could you give us an overview?

Dr. Sarah: Thank you, Mike. This is a demo mode response. To get real AI-generated podcast scripts, please configure your OpenRouter API key.

Mike: That's interesting. What are the key points our listeners should know?

Dr. Sarah: In demo mode, I can only provide this simulated dialogue. The real system would generate natural, informative conversations based on comprehensive research.

Mike: Thank you for that insight, Dr. Sarah.

Dr. Sarah: My pleasure, Mike. Remember to configure your API keys for full functionality!
"""
                podcast_filename = filename
                print("⚠️ Podcast script generated in demo mode")
        except Exception as e:
            print(f"❌ Podcast generation failed: {e}")
            podcast_script = f"Demo podcast script for {topic} - Generation failed: {str(e)}"
            podcast_filename = "demo_failed.wav"
        
        # Display results
        print("\n" + "=" * 60)
        print("📋 RESULTS")
        print("=" * 60)
        
        print("\n📝 Research Report:")
        print("-" * 40)
        print(report[:500] + "..." if len(report) > 500 else report)
        
        print("\n🎙️ Podcast Script:")
        print("-" * 40)
        print(podcast_script[:500] + "..." if len(podcast_script) > 500 else podcast_script)
        
        print(f"\n📁 Files:")
        print(f"  - Report: Available in memory")
        print(f"  - Podcast: {podcast_filename}")
        
        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Main demo function"""
    print_banner()
    
    # Example 1: Simple research
    demo_research("Artificial Intelligence in Healthcare")
    
    print("\n" + "=" * 60)
    print("🎯 To run with your own topic:")
    print("  python simple_demo.py")
    print("  Then modify the topic in the main() function")
    print("\n🔧 To configure APIs:")
    print("  1. Edit .env file with your OpenRouter API key")
    print("  2. Edit .env file with your Douban TTS credentials")
    print("  3. Run the demo again for full functionality")
    print("=" * 60)

if __name__ == "__main__":
    main()
