import os
import wave
from openai import OpenAI
from rich.console import Console
from rich.markdown import Markdown
from dotenv import load_dotenv
from agent.minimax_tts import synthesize_dialogue_sync

load_dotenv()

# Initialize OpenRouter client
def get_openrouter_client(configuration):
    """Get OpenRouter client with configuration"""
    return OpenAI(
        base_url=configuration.openrouter_base_url,
        api_key=configuration.openrouter_api_key or os.getenv("OPENROUTER_API_KEY"),
    )


def display_openrouter_response(response, search_enabled=False):
    """Extract text from OpenRouter response and display as markdown"""
    console = Console()

    # Extract main content from OpenRouter response
    text = response.choices[0].message.content
    md = Markdown(text)
    console.print(md)

    # For search-enabled responses, we'll need to implement web search separately
    # since OpenRouter doesn't have built-in search like Gemini
    sources_text = ""

    if search_enabled:
        console.print("\n" + "="*50)
        console.print("[bold blue]Note: Web search integration needed[/bold blue]")
        console.print("="*50)
        console.print("OpenRouter doesn't have built-in web search. Consider integrating with a separate search API.")

    return text, sources_text


def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2):
    """Save PCM data to a wave file"""
    with wave.open(filename, "wb") as wf:
        wf.setnchannels(channels)
        wf.setsampwidth(sample_width)
        wf.setframerate(rate)
        wf.writeframes(pcm)


def create_podcast_discussion(topic, search_text, video_text, search_sources_text, video_url, filename="research_podcast.wav", configuration=None):
    """Create a 2-speaker podcast discussion explaining the research topic"""

    # Use default values if no configuration provided
    if configuration is None:
        from agent.configuration import Configuration
        configuration = Configuration()

    # Step 1: Generate podcast script using OpenRouter
    client = get_openrouter_client(configuration)

    script_prompt = f"""
    Create a natural, engaging podcast conversation between Dr. Sarah (research expert) and Mike (curious interviewer) about "{topic}".

    Use this research content:

    SEARCH FINDINGS:
    {search_text}

    VIDEO INSIGHTS:
    {video_text}

    Format as a dialogue with:
    - Mike introducing the topic and asking questions
    - Dr. Sarah explaining key concepts and insights
    - Natural back-and-forth discussion (5-7 exchanges)
    - Mike asking follow-up questions
    - Dr. Sarah synthesizing the main takeaways
    - Keep it conversational and accessible (3-4 minutes when spoken)

    Format exactly like this:
    Mike: [opening question]
    Dr. Sarah: [expert response]
    Mike: [follow-up]
    Dr. Sarah: [explanation]
    [continue...]
    """

    extra_headers = {}
    if configuration.openrouter_site_url:
        extra_headers["HTTP-Referer"] = configuration.openrouter_site_url
    if configuration.openrouter_site_name:
        extra_headers["X-Title"] = configuration.openrouter_site_name

    script_response = client.chat.completions.create(
        extra_headers=extra_headers,
        model=configuration.podcast_script_model,
        messages=[{"role": "user", "content": script_prompt}],
        temperature=configuration.podcast_script_temperature
    )

    podcast_script = script_response.choices[0].message.content

    # Step 2: Generate TTS audio using Douban API
    try:
        success = synthesize_dialogue_sync(
            dialogue_text=podcast_script,
            output_file=filename,
            configuration=configuration
        )

        if success:
            print(f"Podcast saved as: {filename}")
        else:
            print(f"TTS generation failed, creating placeholder file: {filename}")
            # Create a placeholder file
            with open(filename, "wb") as f:
                f.write(b"")

    except Exception as e:
        print(f"TTS generation failed: {e}")
        # Create a placeholder file
        with open(filename, "wb") as f:
            f.write(b"")

    return podcast_script, filename


def create_research_report(topic, search_text, video_text, search_sources_text, video_url, configuration=None):
    """Create a comprehensive research report by synthesizing search and video content"""

    # Use default values if no configuration provided
    if configuration is None:
        from agent.configuration import Configuration
        configuration = Configuration()

    # Step 1: Create synthesis using OpenRouter
    client = get_openrouter_client(configuration)

    synthesis_prompt = f"""
    You are a research analyst. I have gathered information about "{topic}" from two sources:

    SEARCH RESULTS:
    {search_text}

    VIDEO CONTENT:
    {video_text}

    Please create a comprehensive synthesis that:
    1. Identifies key themes and insights from both sources
    2. Highlights any complementary or contrasting perspectives
    3. Provides an overall analysis of the topic based on this multi-modal research
    4. Keep it concise but thorough (3-4 paragraphs)

    Focus on creating a coherent narrative that brings together the best insights from both sources.
    """

    extra_headers = {}
    if configuration.openrouter_site_url:
        extra_headers["HTTP-Referer"] = configuration.openrouter_site_url
    if configuration.openrouter_site_name:
        extra_headers["X-Title"] = configuration.openrouter_site_name

    synthesis_response = client.chat.completions.create(
        extra_headers=extra_headers,
        model=configuration.synthesis_model,
        messages=[{"role": "user", "content": synthesis_prompt}],
        temperature=configuration.synthesis_temperature
    )

    synthesis_text = synthesis_response.choices[0].message.content

    # Step 2: Create markdown report
    report = f"""# Research Report: {topic}

## Executive Summary

{synthesis_text}

## Video Source
- **URL**: {video_url}

## Additional Sources
{search_sources_text}

---
*Report generated using multi-modal AI research combining OpenRouter API and video analysis*
"""

    return report, synthesis_text