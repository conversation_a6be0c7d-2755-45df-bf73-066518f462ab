#!/usr/bin/env python3
"""
Test script for OpenRouter API and Douban TTS integrations
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.configuration import Configuration
from agent.utils import get_openrouter_client, display_openrouter_response
from agent.douban_tts import DoubanTTSClient, create_douban_tts_client

load_dotenv()


def test_openrouter_connection():
    """Test OpenRouter API connection"""
    print("🤖 Testing OpenRouter API connection...")
    
    try:
        # Create configuration
        config = Configuration()
        
        # Check if API key is available
        api_key = config.openrouter_api_key or os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OpenRouter API key not found. Please set OPENROUTER_API_KEY in .env file")
            return False
        
        # Create client
        client = get_openrouter_client(config)
        
        # Test simple completion
        response = client.chat.completions.create(
            model=config.search_model,
            messages=[{"role": "user", "content": "Hello! Please respond with 'OpenRouter API is working correctly.'"}],
            temperature=0.0
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenRouter API Response: {result}")
        
        if "OpenRouter API is working correctly" in result:
            print("✅ OpenRouter API integration successful!")
            return True
        else:
            print("⚠️ OpenRouter API responded but with unexpected content")
            return False
            
    except Exception as e:
        print(f"❌ OpenRouter API test failed: {e}")
        return False


async def test_douban_tts_connection():
    """Test Douban TTS API connection"""
    print("\n🎙️ Testing Douban TTS API connection...")
    
    try:
        # Create configuration
        config = Configuration()
        
        # Check if credentials are available
        appid = config.douban_appid or os.getenv("DOUBAN_APPID")
        token = config.douban_token or os.getenv("DOUBAN_TOKEN")
        
        if not appid or not token:
            print("❌ Douban TTS credentials not found. Please set DOUBAN_APPID and DOUBAN_TOKEN in .env file")
            return False
        
        # Create TTS client
        tts_client = create_douban_tts_client(config)
        
        # Test simple synthesis
        test_text = "你好，这是一个测试。"
        output_file = "test_tts_output.wav"
        
        print(f"🔄 Synthesizing text: '{test_text}'")
        
        success = await tts_client.synthesize_text(
            text=test_text,
            voice_type=config.mike_voice,
            output_file=output_file,
            encoding=config.tts_encoding,
            speed_ratio=config.tts_speed_ratio,
            volume_ratio=config.tts_volume_ratio,
            rate=config.tts_rate
        )
        
        if success and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ Douban TTS synthesis successful! Output file: {output_file} ({file_size} bytes)")
            
            # Clean up test file
            try:
                os.remove(output_file)
                print("🧹 Test file cleaned up")
            except:
                pass
                
            return True
        else:
            print("❌ Douban TTS synthesis failed - no output file generated")
            return False
            
    except Exception as e:
        print(f"❌ Douban TTS test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading"""
    print("\n⚙️ Testing configuration...")
    
    try:
        config = Configuration()
        
        print(f"📊 Configuration loaded:")
        print(f"  - OpenRouter Base URL: {config.openrouter_base_url}")
        print(f"  - Search Model: {config.search_model}")
        print(f"  - Synthesis Model: {config.synthesis_model}")
        print(f"  - Video Model: {config.video_model}")
        print(f"  - Podcast Script Model: {config.podcast_script_model}")
        print(f"  - Douban Cluster: {config.douban_cluster}")
        print(f"  - Douban Host: {config.douban_host}")
        print(f"  - Mike Voice: {config.mike_voice}")
        print(f"  - Sarah Voice: {config.sarah_voice}")
        print(f"  - TTS Encoding: {config.tts_encoding}")
        print(f"  - TTS Speed Ratio: {config.tts_speed_ratio}")
        print(f"  - TTS Volume Ratio: {config.tts_volume_ratio}")
        print(f"  - TTS Rate: {config.tts_rate}")
        
        print("✅ Configuration test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting integration tests for Multi-Modal Researcher")
    print("=" * 60)
    
    # Test configuration
    config_ok = test_configuration()
    
    # Test OpenRouter API
    openrouter_ok = test_openrouter_connection()
    
    # Test Douban TTS API
    douban_ok = await test_douban_tts_connection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"  Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"  OpenRouter API: {'✅ PASS' if openrouter_ok else '❌ FAIL'}")
    print(f"  Douban TTS API: {'✅ PASS' if douban_ok else '❌ FAIL'}")
    
    if config_ok and openrouter_ok and douban_ok:
        print("\n🎉 All tests passed! The integrations are working correctly.")
        print("\n📝 Next steps:")
        print("  1. Run the LangGraph server: uvx --refresh --from \"langgraph-cli[inmem]\" --with-editable . --python 3.11 langgraph dev --allow-blocking")
        print("  2. Access the application in your browser")
        print("  3. Test with a research topic and optional video URL")
        return True
    else:
        print("\n❌ Some tests failed. Please check your configuration and API credentials.")
        print("\n🔧 Troubleshooting:")
        if not config_ok:
            print("  - Check that all required modules are installed")
        if not openrouter_ok:
            print("  - Verify your OPENROUTER_API_KEY in the .env file")
            print("  - Check your OpenRouter account balance and permissions")
        if not douban_ok:
            print("  - Verify your DOUBAN_APPID and DOUBAN_TOKEN in the .env file")
            print("  - Check your Douban TTS service status and permissions")
        return False


if __name__ == "__main__":
    # Run the async main function
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
