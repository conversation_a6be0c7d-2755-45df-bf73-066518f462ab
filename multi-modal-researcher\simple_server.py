#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试服务器
"""

import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# 手动加载 .env 文件
def load_env_file():
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    except FileNotFoundError:
        pass

# 加载环境变量
load_env_file()

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_main_page()
        elif parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/test':
            self.send_test_page()
        else:
            self.send_404()
    
    def send_main_page(self):
        # 检查 API 配置状态
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")
        
        openrouter_configured = bool(openrouter_key and 
                                   not openrouter_key.startswith("demo_") and
                                   not openrouter_key.startswith("your_"))
        douban_configured = bool(douban_appid and douban_token and
                               not douban_appid.startswith("demo_") and
                               not douban_token.startswith("demo_") and
                               not douban_appid.startswith("your_") and
                               not douban_token.startswith("your_"))
        
        # 根据配置状态生成状态消息
        if openrouter_configured and douban_configured:
            status_message = "✅ 生产模式：所有 API 已配置完成，系统功能完全启用！"
            status_color = "green"
        elif openrouter_configured:
            status_message = "⚠️ 部分配置：OpenRouter API 已配置，豆包 TTS API 需要配置以启用语音功能。"
            status_color = "orange"
        else:
            status_message = "⚠️ 演示模式：当前运行在演示模式。要启用完整的 AI 生成内容功能，请在 .env 文件中配置您的 API 密钥。"
            status_color = "orange"
        
        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模态研究助手</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .status {{
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid {status_color};
            background-color: {status_color}22;
            color: #333;
        }}
        .config-status {{
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }}
        .nav-links {{
            text-align: center;
            margin: 20px 0;
        }}
        .nav-links a {{
            margin: 0 15px;
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }}
        .nav-links a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 多模态研究助手</h1>
        
        <div class="nav-links">
            <a href="/">🏠 首页</a>
            <a href="/health">💚 健康检查</a>
            <a href="/test">🧪 测试页面</a>
        </div>
        
        <div class="status">
            <strong>{status_message}</strong>
        </div>
        
        <div class="config-status">
            <h3>📊 配置状态</h3>
            <p><strong>OpenRouter API:</strong> <span style="color: {'green' if openrouter_configured else 'red'};">{'✅ 已配置' if openrouter_configured else '❌ 未配置'}</span></p>
            <p><strong>豆包 TTS API:</strong> <span style="color: {'green' if douban_configured else 'red'};">{'✅ 已配置' if douban_configured else '❌ 未配置'}</span></p>
        </div>
        
        <div class="config-status">
            <h3>🎯 核心功能</h3>
            <ul>
                <li><strong>OpenRouter API 集成:</strong> 访问多种大语言模型（GPT-4、Claude、Gemini 等）</li>
                <li><strong>豆包 TTS API 集成:</strong> 高质量中文语音合成服务</li>
                <li><strong>多模态研究:</strong> 结合文本研究与视频内容分析</li>
                <li><strong>播客生成:</strong> AI 生成自然对话和语音合成</li>
            </ul>
        </div>
        
        <div class="config-status">
            <h3>🔧 API 密钥配置</h3>
            <p>在 <code>.env</code> 文件中配置以下密钥：</p>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
# OpenRouter API 配置
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 豆包 TTS API 配置
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here
            </pre>
        </div>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_health_check(self):
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")
        
        openrouter_configured = bool(openrouter_key and 
                                   not openrouter_key.startswith("demo_") and
                                   not openrouter_key.startswith("your_"))
        douban_configured = bool(douban_appid and douban_token and
                               not douban_appid.startswith("demo_") and
                               not douban_token.startswith("demo_") and
                               not douban_appid.startswith("your_") and
                               not douban_token.startswith("your_"))
        
        health_data = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "apis": {
                "openrouter": {
                    "configured": openrouter_configured,
                    "status": "ready" if openrouter_configured else "needs_configuration"
                },
                "douban_tts": {
                    "name": "豆包 TTS API",
                    "configured": douban_configured,
                    "status": "ready" if douban_configured else "needs_configuration"
                }
            },
            "features": {
                "research_generation": True,
                "video_analysis": True,
                "podcast_creation": True,
                "tts_synthesis": douban_configured
            }
        }
        
        self.send_json_response(health_data)
    
    def send_test_page(self):
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试页面</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试页面</h1>
        <p>这是一个简单的测试页面，用于验证服务器是否正常工作。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
    
    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>404 - 页面未找到</title>
</head>
<body>
    <h1>404 - 页面未找到</h1>
    <p><a href="/">返回首页</a></p>
</body>
</html>"""
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        print(f"[{self.address_string()}] {format % args}")

def start_server():
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, SimpleHandler)
    print("🚀 简单测试服务器启动")
    print("=" * 50)
    print(f"🌐 服务器地址: http://127.0.0.1:8000")
    print(f"📚 可用页面:")
    print(f"  - 首页: http://127.0.0.1:8000/")
    print(f"  - 健康检查: http://127.0.0.1:8000/health")
    print(f"  - 测试页面: http://127.0.0.1:8000/test")
    print("=" * 50)
    print("⚡ 按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    start_server()
