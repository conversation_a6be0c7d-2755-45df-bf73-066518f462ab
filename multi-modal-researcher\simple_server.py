#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试服务器
"""

import os
import sys
import json
import asyncio
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
# 添加系统 Python 路径
sys.path.append('C:/Users/<USER>/AppData/Roaming/Python/Python312/site-packages')
sys.path.append('C:/Python312/Lib/site-packages')

# 手动加载 .env 文件
def load_env_file():
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    except FileNotFoundError:
        pass

# 加载环境变量
load_env_file()

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_main_page()
        elif parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/test':
            self.send_test_page()
        elif parsed_path.path == '/research':
            self.send_research_page()
        elif parsed_path.path == '/api/research':
            self.handle_research_request()
        elif parsed_path.path.startswith('/audio/'):
            self.serve_audio_file()
        else:
            self.send_404()
    
    def send_main_page(self):
        # 检查 API 配置状态
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")
        
        openrouter_configured = bool(openrouter_key and 
                                   not openrouter_key.startswith("demo_") and
                                   not openrouter_key.startswith("your_"))
        douban_configured = bool(douban_appid and douban_token and
                               not douban_appid.startswith("demo_") and
                               not douban_token.startswith("demo_") and
                               not douban_appid.startswith("your_") and
                               not douban_token.startswith("your_"))
        
        # 根据配置状态生成状态消息
        if openrouter_configured and douban_configured:
            status_message = "✅ 生产模式：所有 API 已配置完成，系统功能完全启用！"
            status_color = "green"
        elif openrouter_configured:
            status_message = "⚠️ 部分配置：OpenRouter API 已配置，豆包 TTS API 需要配置以启用语音功能。"
            status_color = "orange"
        else:
            status_message = "⚠️ 演示模式：当前运行在演示模式。要启用完整的 AI 生成内容功能，请在 .env 文件中配置您的 API 密钥。"
            status_color = "orange"
        
        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模态研究助手</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .status {{
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid {status_color};
            background-color: {status_color}22;
            color: #333;
        }}
        .config-status {{
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }}
        .nav-links {{
            text-align: center;
            margin: 20px 0;
        }}
        .nav-links a {{
            margin: 0 15px;
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }}
        .nav-links a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 多模态研究助手</h1>
        
        <div class="nav-links">
            <a href="/">🏠 首页</a>
            <a href="/health">💚 健康检查</a>
            <a href="/research">🔬 研究功能</a>
            <a href="/test">🧪 测试页面</a>
        </div>
        
        <div class="status">
            <strong>{status_message}</strong>
        </div>
        
        <div class="config-status">
            <h3>📊 配置状态</h3>
            <p><strong>OpenRouter API:</strong> <span style="color: {'green' if openrouter_configured else 'red'};">{'✅ 已配置' if openrouter_configured else '❌ 未配置'}</span></p>
            <p><strong>豆包 TTS API:</strong> <span style="color: {'green' if douban_configured else 'red'};">{'✅ 已配置' if douban_configured else '❌ 未配置'}</span></p>
        </div>
        
        <div class="config-status">
            <h3>🎯 核心功能</h3>
            <ul>
                <li><strong>OpenRouter API 集成:</strong> 访问多种大语言模型（GPT-4、Claude、Gemini 等）</li>
                <li><strong>豆包 TTS API 集成:</strong> 高质量中文语音合成服务</li>
                <li><strong>多模态研究:</strong> 结合文本研究与视频内容分析</li>
                <li><strong>播客生成:</strong> AI 生成自然对话和语音合成</li>
            </ul>
        </div>
        
        <div class="config-status">
            <h3>🔧 API 密钥配置</h3>
            <p>在 <code>.env</code> 文件中配置以下密钥：</p>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
# OpenRouter API 配置
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 豆包 TTS API 配置
DOUBAN_APPID=your_douban_appid_here
DOUBAN_TOKEN=your_douban_token_here
            </pre>
        </div>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_health_check(self):
        openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
        douban_appid = os.getenv("DOUBAN_APPID", "")
        douban_token = os.getenv("DOUBAN_TOKEN", "")
        
        openrouter_configured = bool(openrouter_key and 
                                   not openrouter_key.startswith("demo_") and
                                   not openrouter_key.startswith("your_"))
        douban_configured = bool(douban_appid and douban_token and
                               not douban_appid.startswith("demo_") and
                               not douban_token.startswith("demo_") and
                               not douban_appid.startswith("your_") and
                               not douban_token.startswith("your_"))
        
        health_data = {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "apis": {
                "openrouter": {
                    "configured": openrouter_configured,
                    "status": "ready" if openrouter_configured else "needs_configuration"
                },
                "douban_tts": {
                    "name": "豆包 TTS API",
                    "configured": douban_configured,
                    "status": "ready" if douban_configured else "needs_configuration"
                }
            },
            "features": {
                "research_generation": True,
                "video_analysis": True,
                "podcast_creation": True,
                "tts_synthesis": douban_configured
            }
        }
        
        self.send_json_response(health_data)

    def send_research_page(self):
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 研究功能</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            display: none;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            margin: 0 15px;
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 AI 研究功能</h1>

        <div class="nav-links">
            <a href="/">🏠 返回首页</a>
            <a href="/health">💚 健康检查</a>
        </div>

        <form id="researchForm">
            <div class="form-group">
                <label for="topic">研究主题：</label>
                <input type="text" id="topic" name="topic" value="人工智能在医疗领域的应用" placeholder="请输入您想研究的主题" required>
            </div>

            <div class="form-group">
                <label for="video_url">视频链接（可选）：</label>
                <input type="text" id="video_url" name="video_url" placeholder="https://youtu.be/example">
            </div>

            <button type="submit" id="submitBtn">🚀 开始 AI 研究</button>
        </form>

        <div id="result" class="result">
            <div id="loading" class="loading" style="display: none;">
                <p>🤖 AI 正在进行研究分析，请稍候...</p>
            </div>
            <div id="content"></div>
        </div>
    </div>

    <script>
        document.getElementById('researchForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const topic = document.getElementById('topic').value;
            const video_url = document.getElementById('video_url').value;
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            const contentDiv = document.getElementById('content');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🤖 AI 研究中...';
            resultDiv.style.display = 'block';
            loadingDiv.style.display = 'block';
            contentDiv.innerHTML = '';

            try {
                const response = await fetch('/api/research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        topic: topic,
                        video_url: video_url
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    contentDiv.innerHTML = `
                        <h3>📊 研究报告</h3>
                        <div style="background: white; padding: 20px; border-radius: 5px; margin: 10px 0;">
                            <pre style="white-space: pre-wrap; font-family: inherit;">${data.report}</pre>
                        </div>

                        <h3>🎙️ 播客脚本</h3>
                        <div style="background: white; padding: 20px; border-radius: 5px; margin: 10px 0;">
                            <pre style="white-space: pre-wrap; font-family: inherit;">${data.podcast_script}</pre>
                        </div>

                        <h3>🎵 播客音频</h3>
                        <p><strong>文件名：</strong>${data.podcast_filename}</p>
                        <audio controls style="width: 100%; margin: 10px 0;">
                            <source src="/audio/${data.podcast_filename}" type="audio/wav">
                            您的浏览器不支持音频播放。
                        </audio>
                        <p><a href="/audio/${data.podcast_filename}" download>📥 下载音频文件</a></p>
                    `;
                } else {
                    contentDiv.innerHTML = `
                        <h3>❌ 错误</h3>
                        <p>${data.message || '研究过程中出现错误'}</p>
                    `;
                }
            } catch (error) {
                contentDiv.innerHTML = `
                    <h3>❌ 网络错误</h3>
                    <p>请求失败：${error.message}</p>
                `;
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 开始 AI 研究';
                loadingDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def handle_research_request(self):
        if self.command != 'POST':
            self.send_error(405, "Method Not Allowed")
            return

        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)

            # 尝试不同的编码方式
            try:
                request_data = json.loads(post_data.decode('utf-8'))
            except UnicodeDecodeError:
                try:
                    request_data = json.loads(post_data.decode('gbk'))
                except UnicodeDecodeError:
                    request_data = json.loads(post_data.decode('latin-1'))

            topic = request_data.get('topic', '')
            video_url = request_data.get('video_url', '')

            if not topic:
                self.send_json_response({
                    "status": "error",
                    "message": "请提供研究主题"
                }, 400)
                return

            # 执行真实的 AI 研究
            result = self.perform_ai_research(topic, video_url)
            self.send_json_response(result)

        except Exception as e:
            self.send_json_response({
                "status": "error",
                "message": f"处理请求时出错：{str(e)}"
            }, 500)

    def perform_ai_research(self, topic, video_url):
        """执行真实的 AI 研究"""
        try:
            # 导入必要的模块
            from agent.configuration import Configuration
            from agent.utils import get_openrouter_client, create_research_report, create_podcast_discussion

            # 创建配置
            config = Configuration()

            # 检查 API 配置
            openrouter_key = os.getenv("OPENROUTER_API_KEY", "")
            if not openrouter_key or openrouter_key.startswith("demo_") or openrouter_key.startswith("your_"):
                return {
                    "status": "error",
                    "message": "OpenRouter API 密钥未配置，请在 .env 文件中设置 OPENROUTER_API_KEY"
                }

            # 模拟搜索和视频分析数据
            search_text = f"关于'{topic}'的研究显示，这是一个快速发展的领域，具有重要的应用价值和发展前景。"
            video_text = f"视频分析显示，{topic}在实际应用中展现出巨大潜力。" if video_url else "未提供视频进行分析。"
            search_sources_text = "来源：学术研究、行业报告、专家观点"

            # 生成研究报告
            report, synthesis = create_research_report(
                topic=topic,
                search_text=search_text,
                video_text=video_text,
                search_sources_text=search_sources_text,
                video_url=video_url,
                configuration=config
            )

            # 生成播客脚本和音频
            safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"research_podcast_{safe_topic.replace(' ', '_')}.wav"

            podcast_script, podcast_filename = create_podcast_discussion(
                topic=topic,
                search_text=search_text,
                video_text=video_text,
                search_sources_text=search_sources_text,
                video_url=video_url,
                filename=filename,
                configuration=config
            )

            return {
                "status": "success",
                "topic": topic,
                "video_url": video_url,
                "report": report,
                "podcast_script": podcast_script,
                "podcast_filename": podcast_filename,
                "message": "AI 研究完成！"
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"AI 研究过程中出错：{str(e)}"
            }

    def serve_audio_file(self):
        """提供音频文件服务"""
        try:
            # 从 URL 中提取文件名
            filename = self.path.split('/')[-1]

            # 安全检查：只允许 .wav 文件
            if not filename.endswith('.wav'):
                self.send_error(404, "File not found")
                return

            # 检查文件是否存在
            if not os.path.exists(filename):
                self.send_error(404, "Audio file not found")
                return

            # 发送音频文件
            self.send_response(200)
            self.send_header('Content-type', 'audio/wav')
            self.send_header('Content-Disposition', f'inline; filename="{filename}"')
            self.end_headers()

            with open(filename, 'rb') as f:
                self.wfile.write(f.read())

        except Exception as e:
            print(f"Error serving audio file: {e}")
            self.send_error(500, "Internal Server Error")

    def do_POST(self):
        """处理 POST 请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path == '/api/research':
            self.handle_research_request()
        else:
            self.send_error(404, "Not Found")

    def send_test_page(self):
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试页面</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试页面</h1>
        <p>这是一个简单的测试页面，用于验证服务器是否正常工作。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
    
    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>404 - 页面未找到</title>
</head>
<body>
    <h1>404 - 页面未找到</h1>
    <p><a href="/">返回首页</a></p>
</body>
</html>"""
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        print(f"[{self.address_string()}] {format % args}")

def start_server():
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, SimpleHandler)
    print("🚀 简单测试服务器启动")
    print("=" * 50)
    print(f"🌐 服务器地址: http://127.0.0.1:8000")
    print(f"📚 可用页面:")
    print(f"  - 首页: http://127.0.0.1:8000/")
    print(f"  - 健康检查: http://127.0.0.1:8000/health")
    print(f"  - 测试页面: http://127.0.0.1:8000/test")
    print("=" * 50)
    print("⚡ 按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    start_server()
