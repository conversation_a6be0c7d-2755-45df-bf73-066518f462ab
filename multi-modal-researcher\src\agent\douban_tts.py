"""
Douban TTS WebSocket API integration
Based on the official tts_websocket_demo and API documentation
"""

import asyncio
import websockets
import uuid
import json
import gzip
import copy
import os
from typing import Optional


class DoubanTTSClient:
    """Douban TTS WebSocket client for text-to-speech conversion"""
    
    def __init__(self, appid: str, token: str, cluster: str = "volcano_tts", 
                 host: str = "openspeech.bytedance.com"):
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.host = host
        self.api_url = f"wss://{host}/api/v1/tts/ws_binary"
        
        # Default header for WebSocket binary protocol
        self.default_header = bytearray(b'\x11\x10\x11\x00')
        
        # Message type constants
        self.MESSAGE_TYPES = {
            11: "audio-only server response", 
            12: "frontend server response", 
            15: "error message from server"
        }
        
    def create_request_json(self, text: str, voice_type: str, reqid: Optional[str] = None,
                           encoding: str = "wav", speed_ratio: float = 1.0, 
                           volume_ratio: float = 1.0, pitch_ratio: float = 1.0,
                           rate: int = 24000) -> dict:
        """Create request JSON for TTS"""
        
        if reqid is None:
            reqid = str(uuid.uuid4())
            
        return {
            "app": {
                "appid": self.appid,
                "token": "access_token",
                "cluster": self.cluster
            },
            "user": {
                "uid": "388808087185088"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "volume_ratio": volume_ratio,
                "pitch_ratio": pitch_ratio,
                "rate": rate
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "text_type": "plain",
                "operation": "submit"
            }
        }
    
    def create_binary_request(self, request_json: dict) -> bytearray:
        """Create binary request from JSON"""
        payload_bytes = str.encode(json.dumps(request_json))
        payload_bytes = gzip.compress(payload_bytes)
        
        full_client_request = bytearray(self.default_header)
        full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        full_client_request.extend(payload_bytes)
        
        return full_client_request
    
    def parse_response(self, res: bytes) -> tuple[bool, Optional[bytes]]:
        """Parse WebSocket response and extract audio data"""
        try:
            protocol_version = res[0] >> 4
            header_size = res[0] & 0x0f
            message_type = res[1] >> 4
            message_type_specific_flags = res[1] & 0x0f
            serialization_method = res[2] >> 4
            message_compression = res[2] & 0x0f
            reserved = res[3]
            
            payload = res[header_size*4:]
            
            if message_type == 0xb:  # audio-only server response
                if message_type_specific_flags == 0:  # no sequence number as ACK
                    return False, None
                else:
                    sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                    payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                    audio_chunk = payload[8:]
                    
                    if sequence_number < 0:
                        return True, audio_chunk  # Last chunk
                    else:
                        return False, audio_chunk  # More chunks coming
                        
            elif message_type == 0xf:  # Error message
                code = int.from_bytes(payload[:4], "big", signed=False)
                msg_size = int.from_bytes(payload[4:8], "big", signed=False)
                error_msg = payload[8:]
                
                if message_compression == 1:
                    error_msg = gzip.decompress(error_msg)
                error_msg = str(error_msg, "utf-8")
                
                print(f"TTS Error {code}: {error_msg}")
                return True, None
                
            elif message_type == 0xc:  # Frontend server response
                msg_size = int.from_bytes(payload[:4], "big", signed=False)
                payload = payload[4:]
                if message_compression == 1:
                    payload = gzip.decompress(payload)
                print(f"Frontend message: {payload}")
                return False, None
                
        except Exception as e:
            print(f"Error parsing response: {e}")
            return True, None
            
        return False, None
    
    async def synthesize_text(self, text: str, voice_type: str, output_file: str,
                             encoding: str = "wav", speed_ratio: float = 1.0,
                             volume_ratio: float = 1.0, rate: int = 24000) -> bool:
        """Synthesize text to speech and save to file"""
        
        try:
            # Create request
            request_json = self.create_request_json(
                text=text,
                voice_type=voice_type,
                encoding=encoding,
                speed_ratio=speed_ratio,
                volume_ratio=volume_ratio,
                rate=rate
            )
            
            binary_request = self.create_binary_request(request_json)
            
            # Prepare headers
            headers = {"Authorization": f"Bearer; {self.token}"}
            
            # Connect and send request
            audio_data = bytearray()
            
            async with websockets.connect(self.api_url, additional_headers=headers, ping_interval=None) as ws:
                await ws.send(binary_request)
                
                while True:
                    response = await ws.recv()
                    done, chunk = self.parse_response(response)
                    
                    if chunk:
                        audio_data.extend(chunk)
                    
                    if done:
                        break
            
            # Save audio file
            if audio_data:
                with open(output_file, "wb") as f:
                    f.write(audio_data)
                print(f"TTS audio saved to: {output_file}")
                return True
            else:
                print("No audio data received")
                return False
                
        except Exception as e:
            print(f"TTS synthesis failed: {e}")
            return False
    
    async def synthesize_dialogue(self, dialogue_text: str, mike_voice: str, 
                                 sarah_voice: str, output_file: str) -> bool:
        """
        Synthesize a dialogue with different voices for Mike and Dr. Sarah
        This is a simplified version - in production you might want to:
        1. Parse the dialogue to separate speakers
        2. Generate separate audio files for each speaker
        3. Combine them with appropriate timing
        """
        
        # For now, we'll use one voice for the entire dialogue
        # You can enhance this to split by speaker and use different voices
        return await self.synthesize_text(
            text=dialogue_text,
            voice_type=mike_voice,  # Using Mike's voice as default
            output_file=output_file
        )


def create_douban_tts_client(configuration) -> DoubanTTSClient:
    """Create Douban TTS client from configuration"""
    return DoubanTTSClient(
        appid=configuration.douban_appid or os.getenv("DOUBAN_APPID", ""),
        token=configuration.douban_token or os.getenv("DOUBAN_TOKEN", ""),
        cluster=configuration.douban_cluster,
        host=configuration.douban_host
    )


# Async wrapper for use in sync contexts
def synthesize_text_sync(text: str, voice_type: str, output_file: str, configuration) -> bool:
    """Synchronous wrapper for TTS synthesis"""
    client = create_douban_tts_client(configuration)
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            client.synthesize_text(
                text=text,
                voice_type=voice_type,
                output_file=output_file,
                encoding=configuration.tts_encoding,
                speed_ratio=configuration.tts_speed_ratio,
                volume_ratio=configuration.tts_volume_ratio,
                rate=configuration.tts_rate
            )
        )
        loop.close()
        return result
    except Exception as e:
        print(f"Sync TTS synthesis failed: {e}")
        return False


def synthesize_dialogue_sync(dialogue_text: str, output_file: str, configuration) -> bool:
    """Synchronous wrapper for dialogue synthesis"""
    client = create_douban_tts_client(configuration)
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            client.synthesize_dialogue(
                dialogue_text=dialogue_text,
                mike_voice=configuration.mike_voice,
                sarah_voice=configuration.sarah_voice,
                output_file=output_file
            )
        )
        loop.close()
        return result
    except Exception as e:
        print(f"Sync dialogue synthesis failed: {e}")
        return False
