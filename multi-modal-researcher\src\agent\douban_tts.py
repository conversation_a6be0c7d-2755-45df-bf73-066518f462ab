"""
豆包 TTS WebSocket API integration
Based on the official tts_websocket_demo and API documentation
火山引擎豆包 TTS API 集成
"""

import asyncio
import websockets
import uuid
import json
import gzip
import copy
import os
from typing import Optional


class DoubanTTSClient:
    """豆包 TTS WebSocket client for text-to-speech conversion"""
    
    def __init__(self, appid: str, token: str, cluster: str = "volcano_tts", 
                 host: str = "openspeech.bytedance.com"):
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.host = host
        self.api_url = f"wss://{host}/api/v1/tts/ws_binary"
        
        # Default header for WebSocket binary protocol
        self.default_header = bytearray(b'\x11\x10\x11\x00')
        
        # Message type constants
        self.MESSAGE_TYPES = {
            11: "audio-only server response", 
            12: "frontend server response", 
            15: "error message from server"
        }
        
    def create_request_json(self, text: str, voice_type: str, reqid: Optional[str] = None,
                           encoding: str = "wav", speed_ratio: float = 1.0, 
                           volume_ratio: float = 1.0, pitch_ratio: float = 1.0,
                           rate: int = 24000) -> dict:
        """Create request JSON for TTS"""
        
        if reqid is None:
            reqid = str(uuid.uuid4())
            
        return {
            "app": {
                "appid": self.appid,
                "token": "access_token",
                "cluster": self.cluster
            },
            "user": {
                "uid": "388808087185088"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "volume_ratio": volume_ratio,
                "pitch_ratio": pitch_ratio,
                "rate": rate
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "text_type": "plain",
                "operation": "submit"
            }
        }
    
    def create_binary_request(self, request_json: dict) -> bytearray:
        """Create binary request from JSON"""
        payload_bytes = str.encode(json.dumps(request_json))
        payload_bytes = gzip.compress(payload_bytes)
        
        full_client_request = bytearray(self.default_header)
        full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        full_client_request.extend(payload_bytes)
        
        return full_client_request
    
    def parse_response(self, res: bytes) -> tuple[bool, Optional[bytes]]:
        """Parse WebSocket response and extract audio data"""
        try:
            protocol_version = res[0] >> 4
            header_size = res[0] & 0x0f
            message_type = res[1] >> 4
            message_type_specific_flags = res[1] & 0x0f
            serialization_method = res[2] >> 4
            message_compression = res[2] & 0x0f
            reserved = res[3]
            
            payload = res[header_size*4:]
            
            if message_type == 0xb:  # audio-only server response
                if message_type_specific_flags == 0:  # no sequence number as ACK
                    return False, None
                else:
                    sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                    payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                    audio_chunk = payload[8:]
                    
                    if sequence_number < 0:
                        return True, audio_chunk  # Last chunk
                    else:
                        return False, audio_chunk  # More chunks coming
                        
            elif message_type == 0xf:  # Error message
                code = int.from_bytes(payload[:4], "big", signed=False)
                msg_size = int.from_bytes(payload[4:8], "big", signed=False)
                error_msg = payload[8:]
                
                if message_compression == 1:
                    error_msg = gzip.decompress(error_msg)
                error_msg = str(error_msg, "utf-8")
                
                print(f"TTS Error {code}: {error_msg}")
                return True, None
                
            elif message_type == 0xc:  # Frontend server response
                msg_size = int.from_bytes(payload[:4], "big", signed=False)
                payload = payload[4:]
                if message_compression == 1:
                    payload = gzip.decompress(payload)
                print(f"Frontend message: {payload}")
                return False, None
                
        except Exception as e:
            print(f"Error parsing response: {e}")
            return True, None
            
        return False, None
    
    async def synthesize_text(self, text: str, voice_type: str, output_file: str,
                             encoding: str = "wav", speed_ratio: float = 1.0,
                             volume_ratio: float = 1.0, rate: int = 24000) -> bool:
        """Synthesize text to speech and save to file"""
        
        try:
            # Create request
            request_json = self.create_request_json(
                text=text,
                voice_type=voice_type,
                encoding=encoding,
                speed_ratio=speed_ratio,
                volume_ratio=volume_ratio,
                rate=rate
            )
            
            binary_request = self.create_binary_request(request_json)
            
            # Prepare headers
            headers = {"Authorization": f"Bearer; {self.token}"}
            
            # Connect and send request
            audio_data = bytearray()
            
            async with websockets.connect(self.api_url, additional_headers=headers, ping_interval=None) as ws:
                await ws.send(binary_request)
                
                while True:
                    response = await ws.recv()
                    done, chunk = self.parse_response(response)
                    
                    if chunk:
                        audio_data.extend(chunk)
                    
                    if done:
                        break
            
            # Save audio file
            if audio_data:
                with open(output_file, "wb") as f:
                    f.write(audio_data)
                print(f"TTS audio saved to: {output_file}")
                return True
            else:
                print("No audio data received")
                return False
                
        except Exception as e:
            print(f"TTS synthesis failed: {e}")
            return False
    
    async def synthesize_dialogue(self, dialogue_text: str, mike_voice: str,
                                 sarah_voice: str, output_file: str) -> bool:
        """
        Synthesize a dialogue with different voices for Mike and Dr. Sarah
        1. Parse the dialogue to separate speakers
        2. Generate separate audio files for each speaker
        3. Combine them with appropriate timing
        """

        try:
            # Parse dialogue into segments
            segments = self.parse_dialogue(dialogue_text)
            if not segments:
                print("No dialogue segments found")
                return False

            # Generate audio for each segment
            audio_segments = []
            temp_files = []

            for i, (speaker, text) in enumerate(segments):
                # Choose voice based on speaker
                voice = mike_voice if speaker.lower() == 'mike' else sarah_voice

                # Create temporary file for this segment
                temp_file = f"temp_segment_{i}_{speaker.lower()}.wav"
                temp_files.append(temp_file)

                print(f"Synthesizing {speaker}: {text[:50]}...")

                # Split long text into smaller chunks if needed
                if len(text) > 500:  # Limit to 500 characters per chunk
                    text_chunks = self.split_text(text, max_length=500)
                    chunk_files = []

                    for j, chunk in enumerate(text_chunks):
                        chunk_file = f"temp_chunk_{i}_{j}_{speaker.lower()}.wav"
                        chunk_success = await self.synthesize_text(
                            text=chunk,
                            voice_type=voice,
                            output_file=chunk_file
                        )
                        if chunk_success:
                            chunk_files.append(chunk_file)

                    # Combine chunks for this speaker
                    if chunk_files:
                        success = self.combine_audio_segments(chunk_files, temp_file)
                        # Clean up chunk files
                        for chunk_file in chunk_files:
                            try:
                                if os.path.exists(chunk_file):
                                    os.remove(chunk_file)
                            except:
                                pass
                    else:
                        success = False
                else:
                    # Synthesize this segment directly
                    success = await self.synthesize_text(
                        text=text,
                        voice_type=voice,
                        output_file=temp_file
                    )

                if success and os.path.exists(temp_file):
                    audio_segments.append(temp_file)
                else:
                    print(f"Failed to synthesize segment for {speaker}")

            # Combine all audio segments
            if audio_segments:
                success = self.combine_audio_segments(audio_segments, output_file)

                # Clean up temporary files
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except:
                        pass

                return success
            else:
                print("No audio segments generated")
                return False

        except Exception as e:
            print(f"Dialogue synthesis failed: {e}")
            return False

    def parse_dialogue(self, dialogue_text: str) -> list[tuple[str, str]]:
        """Parse dialogue text into (speaker, text) segments"""
        segments = []
        lines = dialogue_text.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Look for speaker patterns like "Mike:" or "Dr. Sarah:"
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    speaker = parts[0].strip()
                    text = parts[1].strip()

                    # Normalize speaker names
                    if 'mike' in speaker.lower():
                        speaker = 'Mike'
                    elif 'sarah' in speaker.lower() or 'dr.' in speaker.lower():
                        speaker = 'Dr. Sarah'

                    if text:  # Only add if there's actual text
                        segments.append((speaker, text))

        return segments

    def split_text(self, text: str, max_length: int = 500) -> list[str]:
        """Split long text into smaller chunks at sentence boundaries"""
        if len(text) <= max_length:
            return [text]

        chunks = []
        current_chunk = ""

        # Split by sentences first
        sentences = text.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n')

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # If adding this sentence would exceed the limit
            if len(current_chunk) + len(sentence) > max_length:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # Single sentence is too long, split by commas
                    if len(sentence) > max_length:
                        parts = sentence.split('，')
                        for part in parts:
                            if len(current_chunk) + len(part) > max_length:
                                if current_chunk:
                                    chunks.append(current_chunk.strip())
                                current_chunk = part
                            else:
                                current_chunk += part + '，'
                    else:
                        current_chunk = sentence
            else:
                current_chunk += sentence

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def combine_audio_segments(self, audio_files: list[str], output_file: str) -> bool:
        """Combine multiple WAV files into one"""
        try:
            import wave

            # Read all audio files and combine them
            combined_frames = []
            sample_rate = None
            channels = None
            sample_width = None

            for audio_file in audio_files:
                if not os.path.exists(audio_file):
                    print(f"Audio file not found: {audio_file}")
                    continue

                with wave.open(audio_file, 'rb') as wav_file:
                    # Get audio parameters from first file
                    if sample_rate is None:
                        sample_rate = wav_file.getframerate()
                        channels = wav_file.getnchannels()
                        sample_width = wav_file.getsampwidth()

                    # Read frames
                    frames = wav_file.readframes(wav_file.getnframes())
                    combined_frames.append(frames)

                    # Add a small pause between speakers (0.5 seconds)
                    pause_frames = int(sample_rate * 0.5) * channels * sample_width
                    combined_frames.append(b'\x00' * pause_frames)

            if not combined_frames:
                print("No audio frames to combine")
                return False

            # Write combined audio
            with wave.open(output_file, 'wb') as output_wav:
                output_wav.setnchannels(channels)
                output_wav.setsampwidth(sample_width)
                output_wav.setframerate(sample_rate)

                for frames in combined_frames:
                    output_wav.writeframes(frames)

            print(f"Combined audio saved to: {output_file}")
            return True

        except Exception as e:
            print(f"Audio combination failed: {e}")
            return False


def create_douban_tts_client(configuration) -> DoubanTTSClient:
    """Create 豆包 TTS client from configuration"""
    return DoubanTTSClient(
        appid=configuration.douban_appid or os.getenv("DOUBAN_APPID", ""),
        token=configuration.douban_token or os.getenv("DOUBAN_TOKEN", ""),
        cluster=configuration.douban_cluster,
        host=configuration.douban_host
    )


# Async wrapper for use in sync contexts
def synthesize_text_sync(text: str, voice_type: str, output_file: str, configuration) -> bool:
    """Synchronous wrapper for TTS synthesis"""
    client = create_douban_tts_client(configuration)
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            client.synthesize_text(
                text=text,
                voice_type=voice_type,
                output_file=output_file,
                encoding=configuration.tts_encoding,
                speed_ratio=configuration.tts_speed_ratio,
                volume_ratio=configuration.tts_volume_ratio,
                rate=configuration.tts_rate
            )
        )
        loop.close()
        return result
    except Exception as e:
        print(f"Sync TTS synthesis failed: {e}")
        return False


def synthesize_dialogue_sync(dialogue_text: str, output_file: str, configuration) -> bool:
    """Synchronous wrapper for dialogue synthesis"""
    client = create_douban_tts_client(configuration)
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            client.synthesize_dialogue(
                dialogue_text=dialogue_text,
                mike_voice=configuration.mike_voice,
                sarah_voice=configuration.sarah_voice,
                output_file=output_file
            )
        )
        loop.close()
        return result
    except Exception as e:
        print(f"Sync dialogue synthesis failed: {e}")
        return False
