#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装 MiniMax TTS 依赖的脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装 Python 包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装 {package} 失败: {e}")
        return False

def main():
    print("🚀 开始安装 MiniMax TTS 相关依赖...")
    
    # 需要安装的包
    packages = [
        "requests",
        "asyncio",
        "pathlib",
        "wave"  # 通常是内置的，但确保可用
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✅ 所有依赖安装完成！")
        print("\n📝 下一步:")
        print("1. 在 .env 文件中配置您的 MiniMax API 密钥")
        print("2. 设置 MINIMAX_API_KEY=your_actual_api_key")
        print("3. 运行测试: python test_podcast.py")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装缺失的包")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
